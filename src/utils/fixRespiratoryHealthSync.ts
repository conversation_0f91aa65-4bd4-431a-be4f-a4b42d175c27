import { templateProductSyncService } from '../Questionnaires/Templates/[services]/templateProductSyncService';
import { templateService } from '../Questionnaires/Templates/[services]/templateService';

/**
 * Fix for the Respiratory Health Questionnaire not appearing in products
 * This function will manually sync the template to create the corresponding product
 */
export async function fixRespiratoryHealthSync() {
  try {
    console.log('Starting Respiratory Health Questionnaire sync fix...');
    
    // Get the Respiratory Health Questionnaire template
    const templates = await templateService.getAllTemplates();
    const respiratoryTemplate = templates.find(template => 
      template.title === 'Respiratory Health Questionnaire' || 
      template.title?.includes('Respiratory Health')
    );
    
    if (!respiratoryTemplate) {
      console.error('Respiratory Health Questionnaire template not found');
      return false;
    }
    
    console.log('Found template:', respiratoryTemplate.title, 'ID:', respiratoryTemplate.id);
    console.log('Template published status:', respiratoryTemplate.isPublished);
    
    if (!respiratoryTemplate.isPublished) {
      console.log('Template is not published, cannot create product');
      return false;
    }
    
    // Check if product already exists
    const existingProducts = await templateProductSyncService.getPublishedProducts();
    const existingProduct = existingProducts.find(product => 
      product.templateId === respiratoryTemplate.id
    );
    
    if (existingProduct) {
      console.log('Product already exists:', existingProduct.id);
      return true;
    }
    
    // Create the product for this template
    console.log('Creating product for template...');
    await templateProductSyncService.createProductFromTemplate(respiratoryTemplate);
    
    console.log('Successfully created product for Respiratory Health Questionnaire');
    return true;
    
  } catch (error) {
    console.error('Error fixing Respiratory Health sync:', error);
    return false;
  }
}

/**
 * Run a full sync of all published templates to ensure consistency
 */
export async function runFullTemplateSync() {
  try {
    console.log('Running full template-product sync...');
    await templateProductSyncService.syncAllPublishedTemplates();
    console.log('Full sync completed successfully');
    return true;
  } catch (error) {
    console.error('Error running full sync:', error);
    return false;
  }
}
