import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../[contexts]/CartContext';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import {
  Box,
  Typography,
  Button,
  Stepper,
  Step,
  StepLabel,
  Grid,
  Paper,
  Stack,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Alert,
  Divider,
  Card,
  CardContent,
  Dialog,
  CircularProgress,
  Checkbox,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme
} from '@mui/material';
import {
  CheckCircleOutlined,
  Security,
  CreditCard,
  AccountBalance,
  CheckCircle,
  Receipt,
  ShoppingCart
} from '@mui/icons-material';
import { createPurchase } from '../../[services]/purchaseService';
import { PaymentStatus, Purchase } from '../../[types]/Purchase';
import { Timestamp } from 'firebase/firestore';

const steps = ['Review Order', 'Billing Information', 'Payment', 'Confirmation'];

// Mock payment processing
const processPayment = async (paymentData: any): Promise<{ success: boolean; transactionId: string }> => {
  // Simulate payment processing delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Mock payment success (90% success rate)
  const success = Math.random() > 0.1;
  const transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  return { success, transactionId };
};

const CartCheckout = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { items, clearCart } = useCart();
  const { userData } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [processing, setProcessing] = useState(false);
  const [orderComplete, setOrderComplete] = useState(false);
  const [purchaseId, setPurchaseId] = useState<string>('');
  const [transactionId, setTransactionId] = useState<string>('');

  // Form states
  const [billingInfo, setBillingInfo] = useState({
    firstName: userData?.firstName || '',
    lastName: userData?.lastName || '',
    email: userData?.email || '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    country: 'United States'
  });

  const [paymentInfo, setPaymentInfo] = useState({
    method: 'credit',
    cardNumber: '',
    cardName: '',
    expiryDate: '',
    cvv: '',
    saveCard: false
  });

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0) {
      navigate('/trq/purchases/cart');
    }
  }, [items, navigate]);

  // Calculations
  const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const tax = subtotal * 0.08;
  const total = subtotal + tax;

  const handleNext = () => {
    setActiveStep((prev) => prev + 1);
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const handleFinishOrder = async () => {
    setProcessing(true);
    
    try {
      // Process payment
      const paymentResult = await processPayment({
        method: paymentInfo.method,
        amount: total,
        cardNumber: paymentInfo.cardNumber,
        cardName: paymentInfo.cardName
      });

      if (!paymentResult.success) {
        throw new Error('Payment failed. Please try again.');
      }

      setTransactionId(paymentResult.transactionId);

      // Create purchase record
      const purchaseData: Omit<Purchase, 'id'> = {
        clientId: userData?.uid || '',
        clientName: `${billingInfo.firstName} ${billingInfo.lastName}`,
        questionnaires: items.map(item => ({
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.price
        })),
        totalQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
        totalPrice: total,
        paymentStatus: PaymentStatus.Completed,
        purchaseDate: Timestamp.now(),
        lastUpdated: Timestamp.now(),
        templateId: items[0]?.id || '',
        questionnaireQuantity: items.reduce((sum, item) => sum + item.quantity, 0),
        unitPrice: items[0]?.price || 0,
        subTotal: subtotal,
        tax: tax,
        isPaid: true,
        date: Timestamp.now(),
        paymentType: paymentInfo.method,
        billingInfo,
        transactionId: paymentResult.transactionId
      };

      const newPurchase = await createPurchase(purchaseData);
      setPurchaseId(newPurchase.id);
      
      // Clear cart
      clearCart();
      
      setOrderComplete(true);
      setActiveStep(3);
    } catch (error) {
      console.error('Order processing failed:', error);
      alert(error instanceof Error ? error.message : 'Order processing failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return <ReviewStep items={items} subtotal={subtotal} tax={tax} total={total} />;
      case 1:
        return (
          <BillingStep 
            billingInfo={billingInfo}
            setBillingInfo={setBillingInfo}
          />
        );
      case 2:
        return (
          <PaymentStep 
            paymentInfo={paymentInfo}
            setPaymentInfo={setPaymentInfo}
            total={total}
            processing={processing}
            onFinishOrder={handleFinishOrder}
          />
        );
      case 3:
        return (
          <ConfirmationStep 
            purchaseId={purchaseId}
            transactionId={transactionId}
            total={total}
            orderComplete={orderComplete}
          />
        );
      default:
        return null;
    }
  };

  if (items.length === 0) {
    return null; // Will redirect via useEffect
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h3" gutterBottom sx={{ fontWeight: 600, mb: 4 }}>
        Checkout
      </Typography>

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          {renderStepContent(activeStep)}
        </Grid>
        
        <Grid item xs={12} md={4}>
          <OrderSummary 
            items={items} 
            subtotal={subtotal} 
            tax={tax} 
            total={total}
            activeStep={activeStep}
            onNext={handleNext}
            onBack={handleBack}
            processing={processing}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

// Step Components
const ReviewStep = ({ items, subtotal, tax, total }: any) => (
  <Paper sx={{ p: 3, borderRadius: 2 }}>
    <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
      Review Your Order
    </Typography>
    
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Product</TableCell>
            <TableCell align="center">Quantity</TableCell>
            <TableCell align="right">Price</TableCell>
            <TableCell align="right">Total</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {items.map((item: any) => (
            <TableRow key={item.id}>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ ml: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                      {item.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {item.description}
                    </Typography>
                  </Box>
                </Box>
              </TableCell>
              <TableCell align="center">{item.quantity}</TableCell>
              <TableCell align="right">${item.price.toFixed(2)}</TableCell>
              <TableCell align="right">${(item.price * item.quantity).toFixed(2)}</TableCell>
            </TableRow>
          ))}
          <TableRow>
            <TableCell colSpan={3}>Subtotal</TableCell>
            <TableCell align="right">${subtotal.toFixed(2)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}>Tax</TableCell>
            <TableCell align="right">${tax.toFixed(2)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell colSpan={3}><Typography variant="h6">Total</Typography></TableCell>
            <TableCell align="right"><Typography variant="h6">${total.toFixed(2)}</Typography></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  </Paper>
);

const BillingStep = ({ billingInfo, setBillingInfo }: any) => (
  <Paper sx={{ p: 3, borderRadius: 2 }}>
    <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
      Billing Information
    </Typography>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
      This information is required for billing purposes. All products are delivered digitally.
    </Typography>
    <Grid container spacing={2}>
      <Grid item xs={6}>
        <TextField
          fullWidth
          label="First Name"
          value={billingInfo.firstName}
          onChange={(e) => setBillingInfo({...billingInfo, firstName: e.target.value})}
          required
        />
      </Grid>
      <Grid item xs={6}>
        <TextField
          fullWidth
          label="Last Name"
          value={billingInfo.lastName}
          onChange={(e) => setBillingInfo({...billingInfo, lastName: e.target.value})}
          required
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Email Address"
          type="email"
          value={billingInfo.email}
          onChange={(e) => setBillingInfo({...billingInfo, email: e.target.value})}
          required
          helperText="You'll receive your digital products and receipt at this email"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Phone Number"
          value={billingInfo.phone}
          onChange={(e) => setBillingInfo({...billingInfo, phone: e.target.value})}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Address"
          value={billingInfo.address}
          onChange={(e) => setBillingInfo({...billingInfo, address: e.target.value})}
          required
        />
      </Grid>
      <Grid item xs={6}>
        <TextField
          fullWidth
          label="City"
          value={billingInfo.city}
          onChange={(e) => setBillingInfo({...billingInfo, city: e.target.value})}
          required
        />
      </Grid>
      <Grid item xs={6}>
        <TextField
          fullWidth
          label="Postal Code"
          value={billingInfo.postalCode}
          onChange={(e) => setBillingInfo({...billingInfo, postalCode: e.target.value})}
          required
        />
      </Grid>
    </Grid>
  </Paper>
);

const PaymentStep = ({ paymentInfo, setPaymentInfo, total, processing, onFinishOrder }: any) => (
  <Stack spacing={3}>
    <Paper sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
        Payment Method
      </Typography>
      
      <FormControl component="fieldset" sx={{ mb: 3 }}>
        <RadioGroup
          value={paymentInfo.method}
          onChange={(e) => setPaymentInfo({...paymentInfo, method: e.target.value})}
        >
          <FormControlLabel 
            value="credit" 
            control={<Radio />} 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CreditCard sx={{ mr: 1 }} />
                Credit/Debit Card
              </Box>
            }
          />
          <FormControlLabel 
            value="paypal" 
            control={<Radio />} 
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AccountBalance sx={{ mr: 1 }} />
                PayPal
              </Box>
            }
          />
        </RadioGroup>
      </FormControl>

      {paymentInfo.method === 'credit' && (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Card Number"
              placeholder="1234 5678 9012 3456"
              value={paymentInfo.cardNumber}
              onChange={(e) => setPaymentInfo({...paymentInfo, cardNumber: e.target.value})}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Name on Card"
              value={paymentInfo.cardName}
              onChange={(e) => setPaymentInfo({...paymentInfo, cardName: e.target.value})}
              required
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Expiry Date"
              placeholder="MM/YY"
              value={paymentInfo.expiryDate}
              onChange={(e) => setPaymentInfo({...paymentInfo, expiryDate: e.target.value})}
              required
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="CVV"
              placeholder="123"
              value={paymentInfo.cvv}
              onChange={(e) => setPaymentInfo({...paymentInfo, cvv: e.target.value})}
              required
            />
          </Grid>
        </Grid>
      )}

      {paymentInfo.method === 'paypal' && (
        <Alert severity="info" sx={{ mt: 2 }}>
          You will be redirected to PayPal to complete your payment securely.
        </Alert>
      )}
    </Paper>

    <Paper sx={{ p: 3, borderRadius: 2 }}>
      <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
        <Security color="success" />
        <Typography variant="body2">
          Your payment information is secure and encrypted
        </Typography>
      </Stack>
      
      <Button
        variant="contained"
        size="large"
        fullWidth
        onClick={onFinishOrder}
        disabled={processing}
        sx={{ 
          py: 2, 
          fontSize: '1.1rem',
          background: `linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)`,
          boxShadow: '0 3px 5px 2px rgba(33, 203, 243, .3)',
        }}
      >
        {processing ? (
          <>
            <CircularProgress size={20} sx={{ mr: 1 }} />
            Processing Payment...
          </>
        ) : (
          `Complete Order - $${total.toFixed(2)}`
        )}
      </Button>
    </Paper>
  </Stack>
);

const ConfirmationStep = ({ purchaseId, transactionId, total, orderComplete }: any) => {
  const navigate = useNavigate();
  
  // Auto-redirect to purchases after 10 seconds
  useEffect(() => {
    if (orderComplete) {
      const timer = setTimeout(() => {
        navigate('/trq/purchases/my-purchases');
      }, 10000);
      
      return () => clearTimeout(timer);
    }
  }, [orderComplete, navigate]);

  return (
    <Paper sx={{ p: 4, borderRadius: 2, textAlign: 'center' }}>
      <CheckCircleOutlined sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: 'success.main' }}>
        Order Complete!
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Thank you for your purchase. Your digital products have been processed successfully and will be available in your account shortly.
      </Typography>
      
      <Box sx={{ bgcolor: 'grey.50', p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>Order Details</Typography>
        <Typography>Order ID: <strong>{purchaseId}</strong></Typography>
        <Typography>Transaction ID: <strong>{transactionId}</strong></Typography>
        <Typography>Total: <strong>${total.toFixed(2)}</strong></Typography>
      </Box>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        You will be automatically redirected to your purchases in 10 seconds.
      </Typography>

      <Stack direction="row" spacing={2} justifyContent="center">
        <Button
          variant="outlined"
          onClick={() => navigate('/trq/purchases/my-purchases')}
        >
          View My Orders
        </Button>
        <Button
          variant="contained"
          onClick={() => navigate('/trq/products')}
        >
          Continue Shopping
        </Button>
      </Stack>
    </Paper>
  );
};

const OrderSummary = ({ items, subtotal, tax, total, activeStep, onNext, onBack, processing }: any) => (
  <Paper sx={{ p: 3, borderRadius: 2, position: 'sticky', top: 20 }}>
    <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
      Order Summary
    </Typography>
    
    <Stack spacing={2} sx={{ mb: 3 }}>
      {items.map((item: any) => (
        <Box key={item.id} sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="body2">
            {item.name} × {item.quantity}
          </Typography>
          <Typography variant="body2">
            ${(item.price * item.quantity).toFixed(2)}
          </Typography>
        </Box>
      ))}
    </Stack>

    <Divider sx={{ my: 2 }} />

    <Stack spacing={1}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography>Subtotal</Typography>
        <Typography>${subtotal.toFixed(2)}</Typography>
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography>Digital Delivery</Typography>
        <Typography>FREE</Typography>
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography>Tax</Typography>
        <Typography>${tax.toFixed(2)}</Typography>
      </Box>
      <Divider />
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="h6">Total</Typography>
        <Typography variant="h6">${total.toFixed(2)}</Typography>
      </Box>
    </Stack>

    {activeStep < 2 && (
      <Stack spacing={2} sx={{ mt: 3 }}>
        {activeStep > 0 && (
          <Button variant="outlined" onClick={onBack} fullWidth>
            Back
          </Button>
        )}
        <Button variant="contained" onClick={onNext} fullWidth>
          {activeStep === 0 ? 'Continue to Billing' : 'Continue to Payment'}
        </Button>
      </Stack>
    )}
  </Paper>
);

export default CartCheckout;