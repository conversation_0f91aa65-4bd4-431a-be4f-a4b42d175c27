/**
 * Service for handling respiratory questionnaire instances
 * This service handles instances created from the respiratory questionnaire template
 */

import { 
  collection, 
  addDoc, 
  doc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs,
  Timestamp 
} from 'firebase/firestore';
import { db as firestore } from '../../../Firebase/[config]/firebase';
import { RespiratoryQuestionnaireData } from '../types/RespiratoryQuestionnaireTypes';
import { Questionnaire, QuestionnaireStatus } from '../../[types]/Questionnaire';
import { QuestionnaireTemplate } from '../../[types]/QuestionnaireTemplate';
import { getTemplateById } from '../../Templates/[services]/questionnaireTemplateService';
import { createQuestionnaire, assignQuestionnaireToPatient, submitQuestionnaireResponses } from '../../[services]/questionnaireService';
import { Response } from '../../[types]/Response';

const RESPIRATORY_TEMPLATE_ID = 'respiratory-health-questionnaire-template';

/**
 * Create a respiratory questionnaire instance from the template
 */
export const createRespiratoryQuestionnaireInstance = async (
  assignedPatientUid: string,
  clientId: string,
  assignedBy?: string
): Promise<Questionnaire> => {
  try {
    // Get the respiratory questionnaire template
    const template = await getTemplateById(RESPIRATORY_TEMPLATE_ID);
    
    if (!template) {
      throw new Error('Respiratory questionnaire template not found');
    }

    // Create questionnaire instance from template
    const questionnaireData = {
      title: template.title,
      name: template.name,
      description: template.description,
      status: QuestionnaireStatus.Created,
      isReviewed: false,
      templateId: template.id,
      patientUid: assignedPatientUid,
      clientId,
      hashCode: '',
      isPublished: template.isPublished,
      questions: template.questions,
      version: template.version,
      createdBy: template.createdBy,
      category: template.category,
      tags: template.tags,
      metadata: {
        ...template.metadata,
        wizardType: 'respiratory' // Ensure wizard type is set
      }
    };

    // Create the questionnaire instance
    const createdQuestionnaire = await createQuestionnaire(questionnaireData as any);

    // Assign to patient
    await assignQuestionnaireToPatient(createdQuestionnaire.id, {
      patientUid: assignedPatientUid,
      assignedBy
    });

    return createdQuestionnaire;
  } catch (error) {
    console.error('Error creating respiratory questionnaire instance:', error);
    throw new Error('Failed to create respiratory questionnaire instance');
  }
};

/**
 * Submit a respiratory questionnaire instance response
 */
export const submitRespiratoryQuestionnaireInstance = async (
  questionnaireId: string,
  userId: string,
  data: RespiratoryQuestionnaireData
): Promise<void> => {
  try {
    // Get the questionnaire instance
    const questionnaireRef = doc(firestore, 'questionnaires', questionnaireId);
    const questionnaireDoc = await getDoc(questionnaireRef);

    if (!questionnaireDoc.exists()) {
      throw new Error('Questionnaire instance not found');
    }

    const questionnaire = questionnaireDoc.data() as Questionnaire;

    // Verify this is a respiratory questionnaire
    const isRespiratoryQuestionnaire =
      questionnaire.metadata?.wizardType === 'respiratory' ||
      questionnaire.templateId === RESPIRATORY_TEMPLATE_ID ||
      questionnaire.category === 'Respiratory Health';

    if (!isRespiratoryQuestionnaire) {
      throw new Error('This is not a respiratory questionnaire instance');
    }

    // Convert respiratory data to response format
    const responses: { [questionId: string]: Response } = {};

    // Flatten the respiratory questionnaire data into individual question responses
    const flattenedData = {
      ...data.employeeBackground,
      ...data.generalHealth,
      ...data.additionalQuestions,
      ...data.plhcpQuestions,
      agreedToTerms: data.agreedToTerms,
      finalAgreement: data.finalAgreement,
      signatureName: data.signatureName
    };

    // Create response objects for each field
    Object.keys(flattenedData).forEach((key) => {
      responses[key] = {
        value: flattenedData[key as keyof typeof flattenedData],
        respondedAt: Timestamp.now()
      };
    });

    // Use the standard questionnaire submission service
    await submitQuestionnaireResponses({
      questionnaireId,
      userId,
      responses,
      submittedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error submitting respiratory questionnaire instance:', error);
    throw new Error('Failed to submit respiratory questionnaire instance');
  }
};

/**
 * Get respiratory questionnaire instances for a user
 */
export const getRespiratoryQuestionnaireInstancesForUser = async (
  userUid: string
): Promise<Questionnaire[]> => {
  try {
    const questionnairesCollection = collection(firestore, 'questionnaires');
    
    // Query for respiratory questionnaires assigned to the user
    const q = query(
      questionnairesCollection,
      where('assignedTo.patientUid', '==', userUid),
      where('templateId', '==', RESPIRATORY_TEMPLATE_ID)
    );

    const querySnapshot = await getDocs(q);
    const questionnaires: Questionnaire[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      questionnaires.push({
        id: doc.id,
        ...data
      } as Questionnaire);
    });

    return questionnaires;
  } catch (error) {
    console.error('Error getting respiratory questionnaire instances for user:', error);
    throw new Error('Failed to get respiratory questionnaire instances');
  }
};

/**
 * Check if a respiratory questionnaire template exists
 */
export const checkRespiratoryTemplateExists = async (): Promise<boolean> => {
  try {
    const template = await getTemplateById(RESPIRATORY_TEMPLATE_ID);
    return !!template;
  } catch (error) {
    console.error('Error checking respiratory template existence:', error);
    return false;
  }
};

/**
 * Get the respiratory questionnaire template
 */
export const getRespiratoryQuestionnaireTemplate = async (): Promise<QuestionnaireTemplate | null> => {
  try {
    return await getTemplateById(RESPIRATORY_TEMPLATE_ID);
  } catch (error) {
    console.error('Error getting respiratory questionnaire template:', error);
    return null;
  }
};

/**
 * Batch create respiratory questionnaire instances for multiple users
 */
export const batchCreateRespiratoryInstances = async (
  assignments: Array<{
    patientUid: string;
    clientId: string;
  }>,
  assignedBy?: string
): Promise<Questionnaire[]> => {
  try {
    const createdQuestionnaires: Questionnaire[] = [];

    for (const assignment of assignments) {
      const questionnaire = await createRespiratoryQuestionnaireInstance(
        assignment.patientUid,
        assignment.clientId,
        assignedBy
      );
      createdQuestionnaires.push(questionnaire);
    }

    return createdQuestionnaires;
  } catch (error) {
    console.error('Error batch creating respiratory questionnaire instances:', error);
    throw new Error('Failed to batch create respiratory questionnaire instances');
  }
};
