/**
 * Service for handling respiratory questionnaire data operations
 */

import { 
  collection, 
  addDoc, 
  doc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs,
  Timestamp 
} from 'firebase/firestore';
import { db as firestore } from '../../../Firebase/[config]/firebase';
import { RespiratoryQuestionnaireData } from '../types/RespiratoryQuestionnaireTypes';

const COLLECTION_NAME = 'respiratory_questionnaires';

export interface RespiratoryQuestionnaireSubmission extends RespiratoryQuestionnaireData {
  id?: string;
  userId: string;
  submittedAt: Timestamp;
  status: 'submitted' | 'under_review' | 'approved' | 'requires_followup';
  reviewedBy?: string;
  reviewedAt?: Timestamp;
  complianceReport?: string;
  notes?: string;
}

/**
 * Submit a new respiratory questionnaire
 */
export const submitRespiratoryQuestionnaire = async (
  data: RespiratoryQuestionnaireData,
  userId: string
): Promise<string> => {
  try {
    const submission: Omit<RespiratoryQuestionnaireSubmission, 'id'> = {
      ...data,
      userId,
      submittedAt: Timestamp.now(),
      status: 'submitted'
    };

    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), submission);
    return docRef.id;
  } catch (error) {
    console.error('Error submitting respiratory questionnaire:', error);
    throw new Error('Failed to submit respiratory questionnaire');
  }
};

/**
 * Get a respiratory questionnaire by ID
 */
export const getRespiratoryQuestionnaireById = async (
  id: string
): Promise<RespiratoryQuestionnaireSubmission | null> => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data()
      } as RespiratoryQuestionnaireSubmission;
    }

    return null;
  } catch (error) {
    console.error('Error getting respiratory questionnaire:', error);
    throw new Error('Failed to get respiratory questionnaire');
  }
};

/**
 * Get all respiratory questionnaires for a user
 */
export const getRespiratoryQuestionnairesByUser = async (
  userId: string
): Promise<RespiratoryQuestionnaireSubmission[]> => {
  try {
    const q = query(
      collection(firestore, COLLECTION_NAME),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(q);
    const questionnaires: RespiratoryQuestionnaireSubmission[] = [];

    querySnapshot.forEach((doc) => {
      questionnaires.push({
        id: doc.id,
        ...doc.data()
      } as RespiratoryQuestionnaireSubmission);
    });

    return questionnaires;
  } catch (error) {
    console.error('Error getting user respiratory questionnaires:', error);
    throw new Error('Failed to get user respiratory questionnaires');
  }
};

/**
 * Update the status of a respiratory questionnaire (for reviewers)
 */
export const updateRespiratoryQuestionnaireStatus = async (
  id: string,
  status: RespiratoryQuestionnaireSubmission['status'],
  reviewedBy: string,
  notes?: string
): Promise<void> => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    
    const updateData: Partial<RespiratoryQuestionnaireSubmission> = {
      status,
      reviewedBy,
      reviewedAt: Timestamp.now()
    };

    if (notes) {
      updateData.notes = notes;
    }

    await updateDoc(docRef, updateData);
  } catch (error) {
    console.error('Error updating respiratory questionnaire status:', error);
    throw new Error('Failed to update respiratory questionnaire status');
  }
};

/**
 * Get all respiratory questionnaires for review (for healthcare providers)
 */
export const getRespiratoryQuestionnairesForReview = async (): Promise<RespiratoryQuestionnaireSubmission[]> => {
  try {
    const q = query(
      collection(firestore, COLLECTION_NAME),
      where('status', 'in', ['submitted', 'under_review'])
    );

    const querySnapshot = await getDocs(q);
    const questionnaires: RespiratoryQuestionnaireSubmission[] = [];

    querySnapshot.forEach((doc) => {
      questionnaires.push({
        id: doc.id,
        ...doc.data()
      } as RespiratoryQuestionnaireSubmission);
    });

    return questionnaires;
  } catch (error) {
    console.error('Error getting respiratory questionnaires for review:', error);
    throw new Error('Failed to get respiratory questionnaires for review');
  }
};

/**
 * Generate a compliance report for a respiratory questionnaire
 */
export const generateComplianceReport = async (
  id: string,
  report: string
): Promise<void> => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    
    await updateDoc(docRef, {
      complianceReport: report,
      status: 'approved'
    });
  } catch (error) {
    console.error('Error generating compliance report:', error);
    throw new Error('Failed to generate compliance report');
  }
};

/**
 * Check if a user has already submitted a respiratory questionnaire
 */
export const hasUserSubmittedRespiratoryQuestionnaire = async (
  userId: string
): Promise<boolean> => {
  try {
    const questionnaires = await getRespiratoryQuestionnairesByUser(userId);
    return questionnaires.length > 0;
  } catch (error) {
    console.error('Error checking user respiratory questionnaire submission:', error);
    return false;
  }
};
