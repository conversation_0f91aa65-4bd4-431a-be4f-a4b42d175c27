/**
 * Types and interfaces for the Respiratory Health Questionnaire
 * Based on OSHA mandated respiratory questionnaire requirements
 */

export interface EmployeeBackgroundInfo {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  ssn: string;
  birthdate: string;
  
  // Physical Characteristics
  gender: 'male' | 'female';
  heightFeet: number;
  heightInches: number;
  weight: number;
  
  // Job Information
  jobTitle: string;
  bestTimeToCall: string;
  
  // Respirator History
  previouslyWornRespirator: boolean;
  
  // Respirator Types (multi-select)
  respiratorTypes: string[];
  respiratorTypesOther?: string;
  
  // Exposure Risks (multi-select)
  exposureRisks: string[];
}

export interface GeneralHealthInfo {
  // Smoking
  currentSmoker: boolean;
  
  // Medical Conditions
  seizures: boolean;
  diabetes: boolean;
  allergicReactions: boolean;
  claustrophobia: boolean;
  troubleSmellingOdors: boolean;
  
  // Pulmonary Problems
  asbestosis: boolean;
  asthma: boolean;
  asthmaInhaler3Years?: boolean;
  asthmaEmergency3Years?: boolean;
  chronicBronchitis: boolean;
  chronicBronchitis3Years?: boolean;
  emphysema: boolean;
  pneumonia: boolean;
  pneumonia3Years?: boolean;
  tuberculosis: boolean;
  silicosis: boolean;
  pneumothorax: boolean;
  pneumothorax3Years?: boolean;
  lungCancer: boolean;
  brokenRibs: boolean;
  currentRibPain?: boolean;
  chestInjuriesSurgeries: boolean;
  chestInjuriesSurgeries3Years?: boolean;
  otherLungProblems: boolean;
  otherLungProblems3Years?: boolean;
  
  // Pulmonary Symptoms
  shortnessOfBreath: boolean;
  shortnessOfBreathWalking: boolean;
  shortnessOfBreathOrdinaryPace: boolean;
  stopForBreath: boolean;
  shortnessOfBreathDressing: boolean;
  shortnessOfBreathJob: boolean;
  coughingPhlegm: boolean;
  coughingPhlegm3Months?: boolean;
  coughingMorning: boolean;
  coughingMorning3Months?: boolean;
  coughingLyingDown: boolean;
  coughingBlood: boolean;
  wheezing: boolean;
  wheezingJob: boolean;
  chestPainBreathing: boolean;
  otherLungSymptoms: boolean;
  otherLungSymptomsDescription?: string;
  
  // Cardiovascular Problems
  heartAttack: boolean;
  stroke: boolean;
  angina: boolean;
  heartFailure: boolean;
  legSwelling: boolean;
  heartArrhythmia: boolean;
  highBloodPressure: boolean;
  bloodPressureNormal?: boolean;
  otherHeartProblems: boolean;
  otherHeartProblemsDescription?: string;
  
  // Cardiovascular Symptoms
  chestPainTightness: boolean;
  chestPainActivity: boolean;
  chestPainJob: boolean;
  heartSkipping: boolean;
  heartburn: boolean;
  otherHeartSymptoms: boolean;
  otherHeartSymptomsDescription?: string;
  
  // Medications
  breathingMedication: boolean;
  breathingMedicationDetails?: string;
  heartMedication: boolean;
  heartMedicationDetails?: string;
  bloodPressureMedication: boolean;
  bloodPressureMedicationDetails?: string;
  bloodPressureMedicationNormal?: boolean;
  seizureMedication: boolean;
  
  // Respirator Issues
  eyeIrritation: boolean;
  eyeIrritationCurrent?: boolean;
  skinAllergies: boolean;
  skinAllergiesCurrent?: boolean;
  anxiety: boolean;
  anxietyCurrent?: boolean;
  weakness: boolean;
  weaknessCurrent?: boolean;
  otherRespiratorProblems: boolean;
  otherRespiratorProblemsCurrent?: boolean;
  otherRespiratorProblemsDescription?: string;
}

export interface AdditionalQuestions {
  // Vision
  lostVision: boolean;
  lostVision3Days?: boolean;
  needContactLenses: boolean;
  needGlasses: boolean;
  colorBlindness: boolean;
  identifyColors?: boolean;
  otherVisionProblems: boolean;
  monocularVision?: boolean;
  
  // Hearing
  earInjury: boolean;
  earInjuryRepaired?: boolean;
  perforatedEardrum?: boolean;
  difficultyHearing: boolean;
  normalAudiogram?: boolean;
  needHearingAid: boolean;
  hearingAidCorrects?: boolean;
  otherHearingProblems: boolean;
  otherHearingProblemsDescription?: string;
  
  // Back
  backInjury: boolean;
  backInjuryHospitalized?: boolean;
  backSurgery3Years?: boolean;
  backInjuryIssues?: boolean;
  spinalFracture?: boolean;
  
  // Musculoskeletal
  weakness: boolean;
  backPain: boolean;
  backPain3Years?: boolean;
  difficultyMovingArmsLegs: boolean;
  painStiffnessWaist: boolean;
  difficultyMovingHead: boolean;
  difficultyMovingHeadSide: boolean;
  difficultyBendingKnees: boolean;
  difficultySquatting: boolean;
  difficultyClimbingStairs: boolean;
  otherMusculoskeletalProblems: boolean;
  otherMusculoskeletalProblemsDescription?: string;
}

export interface PLHCPQuestions {
  // Work Environment
  highAltitude: boolean;
  highAltitudeSymptoms?: boolean;
  hazardousExposure: boolean;
  hazardousExposureChemicals?: string;
  
  // Material Exposures
  asbestosExposure: boolean;
  silicaExposure: boolean;
  tungstenCobaltExposure: boolean;
  berylliumExposure: boolean;
  aluminumExposure: boolean;
  coalExposure: boolean;
  ironExposure: boolean;
  tinExposure: boolean;
  dustyEnvironments: boolean;
  otherHazardousExposures: boolean;
  otherHazardousExposuresDescription?: string;
  
  // Work History
  secondJobs?: string;
  previousOccupations?: string;
  hobbies?: string;
  
  // Military
  militaryService: boolean;
  militaryExposure?: boolean;
  
  // HAZMAT
  hazmatTeam: boolean;
  
  // Other Medications
  otherMedications: boolean;
  otherMedicationsNames?: string;
  
  // Respirator Equipment
  hepaFilters: boolean;
  canisters: boolean;
  cartridges: boolean;
  
  // Usage Frequency
  escapeOnly: boolean;
  emergencyRescue: boolean;
  lessThan5Hours: boolean;
  lessThan2Hours: boolean;
  twoTo4Hours: boolean;
  over4Hours: boolean;
  
  // Work Effort
  lightWork: boolean;
  lightWorkDuration?: string;
  moderateWork: boolean;
  moderateWorkDuration?: string;
  heavyWork: boolean;
  heavyWorkDuration?: string;
  
  // Work Conditions
  protectiveClothing: boolean;
  protectiveClothingDescription?: string;
  hotConditions: boolean;
  humidConditions: boolean;
  workDescription?: string;
  hazardousConditions?: string;
  
  // Toxic Substances
  toxicSubstance1?: string;
  toxicSubstance1Exposure?: string;
  toxicSubstance1Duration?: string;
  toxicSubstance2?: string;
  toxicSubstance2Exposure?: string;
  toxicSubstance2Duration?: string;
  otherToxicSubstances?: string;
  
  // Special Responsibilities
  specialResponsibilities?: string;
  
  // Cancer History
  cancer: boolean;
  boneCancer?: boolean;
  brainCancer?: boolean;
  breastCancer?: boolean;
  colonCancer?: boolean;
  lungCancer?: boolean;
  pancreaticCancer?: boolean;
  prostateCancer?: boolean;
  skinCancer?: boolean;
  smallIntestineCancer?: boolean;
  stomachCancer?: boolean;
  throatCancer?: boolean;
  otherCancer?: string;
}

export interface RespiratoryQuestionnaireData {
  // Agreement and Terms
  agreedToTerms: boolean;
  
  // Main sections
  employeeBackground: EmployeeBackgroundInfo;
  generalHealth: GeneralHealthInfo;
  additionalQuestions: AdditionalQuestions;
  plhcpQuestions: PLHCPQuestions;
  
  // Final agreement
  finalAgreement: boolean;
  signatureName: string;
  
  // Metadata
  submittedAt?: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  complianceStatus?: 'pending' | 'approved' | 'requires_followup';
}

// Constants for dropdown options
export const RESPIRATOR_TYPES = [
  'Half Mask',
  'Full facepiece mask',
  'Helmet Hood',
  'Escape',
  'PAPR',
  'SCBA',
  'Supplied-air/ Air-line',
  'Other'
];

export const EXPOSURE_RISKS = [
  'Asbestos',
  'Silica or Tungsten',
  'Smoke or Dust',
  'Heavy Metals',
  'Hazardous Waste',
  'Irritant Fumes',
  'Temperature Extreme'
];
