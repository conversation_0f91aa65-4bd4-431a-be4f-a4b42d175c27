/**
 * Respiratory Questionnaire Instance Wizard
 * 
 * This component handles respiratory questionnaire instances created from templates.
 * It preserves the existing wizard interface while working with the template-to-instance pattern.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useForm, FormProvider } from 'react-hook-form';
import { useSnackbar } from 'notistack';
import { motion } from 'framer-motion';

// Material-UI components
import {
  Container,
  Box,
  Paper,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';

// Import custom components (reuse existing ones)
import PersonalInfoSection from './PersonalInfoSection';
import ConditionalBooleanQuestion from './ConditionalBooleanQuestion';
import GroupedBooleanQuestions from './GroupedBooleanQuestions';
import CheckboxListSection from './CheckboxListSection';
import ValidationErrorBox from './ValidationErrorBox';

// Import types and services
import { RespiratoryQuestionnaireData } from '../types/RespiratoryQuestionnaireTypes';
import { RESPIRATOR_TYPES, EXPOSURE_RISKS } from '../data/respiratoryQuestionnaireTemplate';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { getQuestionnaireById, submitQuestionnaireResponses } from 'Questionnaires/[services]/questionnaireService';
import { Questionnaire } from 'Questionnaires/[types]/Questionnaire';
import { Response } from 'Questionnaires/[types]/Response';
import { Timestamp } from 'firebase/firestore';

interface RespiratoryInstanceWizardProps {
  questionnaireId?: string;
  questionnaire?: Questionnaire;
}

const RespiratoryInstanceWizard: React.FC<RespiratoryInstanceWizardProps> = ({ 
  questionnaireId: propQuestionnaireId, 
  questionnaire: propQuestionnaire 
}) => {
  const { id: paramQuestionnaireId } = useParams<{ id: string }>();
  const questionnaireId = propQuestionnaireId || paramQuestionnaireId;
  
  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(!propQuestionnaire);
  const [questionnaire, setQuestionnaire] = useState<Questionnaire | null>(propQuestionnaire || null);
  const [error, setError] = useState<string | null>(null);
  
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const { userData } = useAuth();

  const methods = useForm<RespiratoryQuestionnaireData>({
    mode: 'onBlur',
    defaultValues: {
      agreedToTerms: false,
      employeeBackground: {
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        ssn: '',
        birthdate: '',
        gender: 'male',
        heightFeet: 0,
        heightInches: 0,
        weight: 0,
        jobTitle: '',
        bestTimeToCall: '',
        previouslyWornRespirator: false,
        respiratorTypes: [],
        exposureRisks: []
      }
    }
  });

  const steps = [
    'Terms & Employee Info',
    'General Health',
    'Additional Questions', 
    'PLHCP Questions',
    'Review & Submit'
  ];

  // Load questionnaire if not provided as prop
  useEffect(() => {
    const loadQuestionnaire = async () => {
      if (!questionnaireId || propQuestionnaire) return;
      
      try {
        setLoading(true);
        const questionnaireData = await getQuestionnaireById(questionnaireId);
        
        if (!questionnaireData) {
          setError('Questionnaire not found');
          return;
        }

        // Verify this is a respiratory questionnaire
        const isRespiratoryQuestionnaire = 
          questionnaireData.metadata?.wizardType === 'respiratory' ||
          questionnaireData.templateId === 'respiratory-health-questionnaire-template' ||
          questionnaireData.category === 'Respiratory Health';

        if (!isRespiratoryQuestionnaire) {
          setError('This questionnaire is not a respiratory health questionnaire');
          return;
        }

        setQuestionnaire(questionnaireData);
      } catch (err) {
        console.error('Error loading questionnaire:', err);
        setError('Failed to load questionnaire');
      } finally {
        setLoading(false);
      }
    };

    loadQuestionnaire();
  }, [questionnaireId, propQuestionnaire]);

  const handleNext = async () => {
    const isValid = await methods.trigger();
    if (isValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = async (data: RespiratoryQuestionnaireData) => {
    if (!userData?.uid || !questionnaire) {
      enqueueSnackbar('User not authenticated or questionnaire not loaded. Please try again.', { variant: 'error' });
      return;
    }

    setIsSubmitting(true);
    try {
      // Convert form data to response format
      const responses: { [questionId: string]: Response } = {};

      // Flatten the respiratory questionnaire data into individual question responses
      const flattenedData = {
        ...data.employeeBackground,
        ...data.generalHealth,
        ...data.additionalQuestions,
        ...data.plhcpQuestions,
        agreedToTerms: data.agreedToTerms,
        finalAgreement: data.finalAgreement,
        signatureName: data.signatureName
      };

      // Create response objects for each field
      Object.keys(flattenedData).forEach((key) => {
        responses[key] = {
          value: flattenedData[key as keyof typeof flattenedData],
          respondedAt: Timestamp.now()
        };
      });

      // Submit the questionnaire response using the standard service
      await submitQuestionnaireResponses({
        questionnaireId: questionnaire.id,
        userId: userData.uid,
        responses: responses,
        submittedAt: new Date().toISOString()
      });

      enqueueSnackbar('Respiratory questionnaire submitted successfully!', { variant: 'success' });

      // Navigate to appropriate page based on user role
      setTimeout(() => {
        if (userData.role === 'Patient') {
          navigate('/trq/my-questionnaires');
        } else {
          navigate('/trq/questionnaires');
        }
      }, 2000);

    } catch (error) {
      console.error('Error submitting respiratory questionnaire:', error);
      enqueueSnackbar('Error submitting questionnaire. Please try again.', { variant: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <Container maxWidth="sm" sx={{ pt: 6 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <CircularProgress />
          <Typography sx={{ mt: 2 }}>Loading questionnaire...</Typography>
        </Paper>
      </Container>
    );
  }

  // Show error state
  if (error || !questionnaire) {
    return (
      <Container maxWidth="sm" sx={{ pt: 6 }}>
        <Paper sx={{ p: 4 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error || 'Questionnaire not found'}
          </Alert>
          <Button onClick={() => navigate('/trq/questionnaires')}>
            Back to Questionnaires
          </Button>
        </Paper>
      </Container>
    );
  }

  const { control, formState: { errors } } = methods;

  return (
    <Container maxWidth="md" sx={{ px: { xs: 2, sm: 3 } }}>
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <Box sx={{ my: { xs: 2, sm: 4 } }}>
          {/* Header */}
          <Paper sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            borderRadius: '12px',
            border: '1px solid #e0e0e0'
          }} elevation={3}>
            <Typography variant="h4" gutterBottom sx={{
              fontSize: { xs: '1.75rem', sm: '2.125rem' },
              fontWeight: 600
            }}>
              {questionnaire.title}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" paragraph sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}>
              {questionnaire.description}
            </Typography>
          </Paper>

          {/* Stepper */}
          <Paper sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            borderRadius: '12px'
          }} elevation={3}>
            <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 2 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Paper>

          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(handleSubmit)}>
              {/* Main Content */}
              <Paper sx={{
                p: { xs: 2, sm: 3 },
                mb: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                borderRadius: '12px',
                minHeight: '400px'
              }} elevation={3}>
                {activeStep === 0 && (
                  <Box>
                    <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                      Terms of Agreement & Employee Information
                    </Typography>

                    <PersonalInfoSection
                      control={control}
                      errors={errors.employeeBackground || {}}
                      respiratorTypes={RESPIRATOR_TYPES}
                      exposureRisks={EXPOSURE_RISKS}
                    />
                  </Box>
                )}

                {activeStep === 1 && (
                  <Box>
                    <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                      Part 2 - (Mandatory)
                    </Typography>

                    <GroupedBooleanQuestions
                      title="General Health Information"
                      questions={[
                        {
                          name: 'generalHealth.currentSmoker',
                          label: 'Do you currently smoke tobacco, or have you smoked tobacco in the last month?',
                          required: true
                        }
                      ]}
                      control={control}
                      errors={errors.generalHealth || {}}
                    />
                  </Box>
                )}

                {activeStep === 2 && (
                  <Box>
                    <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                      Part 3 - Additional Questions
                    </Typography>

                    <GroupedBooleanQuestions
                      title="Vision and Hearing"
                      questions={[
                        {
                          name: 'additionalQuestions.visionProblems',
                          label: 'Do you have vision problems that interfere with your job?',
                          required: true
                        }
                      ]}
                      control={control}
                      errors={errors.additionalQuestions || {}}
                    />
                  </Box>
                )}

                {activeStep === 3 && (
                  <Box>
                    <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                      Part 4 - PLHCP (Discretionary Questions)
                    </Typography>

                    <GroupedBooleanQuestions
                      title="Occupational Health"
                      questions={[
                        {
                          name: 'plhcpQuestions.cancer',
                          label: 'Have you ever been diagnosed with cancer?',
                          required: true
                        }
                      ]}
                      control={control}
                      errors={errors.plhcpQuestions || {}}
                    />
                  </Box>
                )}

                {activeStep === 4 && (
                  <Box>
                    <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                      Review & Submit
                    </Typography>

                    <Typography variant="body1" paragraph>
                      Please review your responses and submit the questionnaire.
                    </Typography>

                    <ValidationErrorBox errors={errors} />
                  </Box>
                )}
              </Paper>

              {/* Navigation */}
              <Paper sx={{
                p: { xs: 2, sm: 3 },
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                borderRadius: '12px'
              }} elevation={3}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Button
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    variant="outlined"
                    sx={{ mr: 1 }}
                  >
                    Back
                  </Button>

                  <Box sx={{ flex: '1 1 auto' }} />

                  {activeStep === steps.length - 1 ? (
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={isSubmitting}
                      sx={{ minWidth: 120 }}
                    >
                      {isSubmitting ? <CircularProgress size={24} /> : 'Submit'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleNext}
                      sx={{ minWidth: 120 }}
                    >
                      Next
                    </Button>
                  )}
                </Box>
              </Paper>
            </form>
          </FormProvider>
        </Box>
      </motion.div>
    </Container>
  );
};

export default RespiratoryInstanceWizard;
