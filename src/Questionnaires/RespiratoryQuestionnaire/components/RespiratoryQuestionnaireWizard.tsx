import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Container, 
  Paper, 
  Typography, 
  Stepper,
  Step,
  StepLabel,
  Alert,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { useForm, FormProvider } from 'react-hook-form';
import { motion } from 'framer-motion';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../Authentication/[contexts]/AuthContext';

// Import custom components
import PersonalInfoSection from './PersonalInfoSection';
import ConditionalBooleanQuestion from './ConditionalBooleanQuestion';
import GroupedBooleanQuestions from './GroupedBooleanQuestions';
import CheckboxListSection from './CheckboxListSection';
import ValidationErrorBox from './ValidationErrorBox';

// Import data and services
import { respiratoryQuestionnaireTemplate, RESPIRATOR_TYPES, EXPOSURE_RISKS } from '../data/respiratoryQuestionnaireTemplate';
import { RespiratoryQuestionnaireData } from '../types/RespiratoryQuestionnaireTypes';
import { submitRespiratoryQuestionnaire } from '../services/respiratoryQuestionnaireService';

const RespiratoryQuestionnaireWizard: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const { userData } = useAuth();

  const methods = useForm<RespiratoryQuestionnaireData>({
    mode: 'onBlur',
    defaultValues: {
      agreedToTerms: false,
      employeeBackground: {
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        ssn: '',
        birthdate: '',
        gender: 'male',
        heightFeet: 0,
        heightInches: 0,
        weight: 0,
        jobTitle: '',
        bestTimeToCall: '',
        previouslyWornRespirator: false,
        respiratorTypes: [],
        exposureRisks: []
      }
    }
  });

  const steps = [
    'Terms & Employee Info',
    'General Health',
    'Additional Questions', 
    'PLHCP Questions',
    'Review & Submit'
  ];

  const handleNext = async () => {
    const isValid = await methods.trigger();
    if (isValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = async (data: RespiratoryQuestionnaireData) => {
    if (!userData?.uid) {
      enqueueSnackbar('User not authenticated. Please log in and try again.', { variant: 'error' });
      return;
    }

    setIsSubmitting(true);
    try {
      const submissionId = await submitRespiratoryQuestionnaire(data, userData.uid);

      enqueueSnackbar('Respiratory questionnaire submitted successfully!', { variant: 'success' });

      // Navigate to success page or questionnaires list
      setTimeout(() => {
        navigate('/trq/questionnaires');
      }, 2000);

    } catch (error) {
      console.error('Error submitting respiratory questionnaire:', error);
      enqueueSnackbar('Error submitting questionnaire. Please try again.', { variant: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = (step: number) => {
    const { control, formState: { errors } } = methods;

    switch (step) {
      case 0:
        return (
          <Box>
            {/* Terms Agreement */}
            <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                Part 1 - (Mandatory)
              </Typography>
              
              <FormControlLabel
                control={
                  <Checkbox
                    {...methods.register('agreedToTerms', { required: 'You must agree to the terms to continue' })}
                    color="primary"
                  />
                }
                label={
                  <Typography variant="body2">
                    I have read and agree to the{' '}
                    <a href="#" style={{ color: '#1976d2' }}>Terms and Conditions</a>,{' '}
                    <a href="#" style={{ color: '#1976d2' }}>Privacy Policy</a>, and{' '}
                    <a href="#" style={{ color: '#1976d2' }}>Business Associate Agreement</a>
                  </Typography>
                }
                sx={{ mb: 2 }}
              />
              
              {errors.agreedToTerms && (
                <ValidationErrorBox message={errors.agreedToTerms.message || 'Please select a value.'} />
              )}
              
              <Typography variant="body2" color="text.secondary" paragraph>
                If you are utilizing this questionnaire, you attest that you can read and that you understand this does not constitute as a patient provider relationship.
              </Typography>
            </Paper>

            {/* Personal Information */}
            <PersonalInfoSection control={control} errors={errors} />

            {/* Previous Respirator Use */}
            <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
              <ConditionalBooleanQuestion
                name="employeeBackground.previouslyWornRespirator"
                control={control}
                label="Have you previously worn a respirator?"
                required={true}
                error={!!errors.employeeBackground?.previouslyWornRespirator}
                helperText={errors.employeeBackground?.previouslyWornRespirator?.message}
              />
              
              {errors.employeeBackground?.previouslyWornRespirator && (
                <ValidationErrorBox message="Please select a value." />
              )}
            </Paper>

            {/* Respirator Types */}
            <CheckboxListSection
              title="Type of Respirators the employee may be using include:"
              name="employeeBackground.respiratorTypes"
              options={RESPIRATOR_TYPES}
              control={control}
              errors={errors.employeeBackground || {}}
              hasOtherOption={true}
              otherOptionName="employeeBackground.respiratorTypesOther"
              otherOptionLabel="Please describe"
            />

            {/* Exposure Risks */}
            <CheckboxListSection
              title="Exposure Risks May Include"
              name="employeeBackground.exposureRisks"
              options={EXPOSURE_RISKS}
              control={control}
              errors={errors.employeeBackground || {}}
            />

            {/* Workload Information */}
            <Paper elevation={1} sx={{ p: 3, mb: 3, bgcolor: 'grey.50' }}>
              <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
                Workload Information
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Additional workload information will be collected in the PLHCP section.
              </Typography>
            </Paper>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
              Part 2 - (Mandatory)
            </Typography>
            
            <GroupedBooleanQuestions
              title="General Health Information"
              questions={[
                {
                  name: 'generalHealth.currentSmoker',
                  label: 'Do you currently smoke tobacco, or have you smoked tobacco in the last month?',
                  required: true
                }
              ]}
              control={control}
              errors={errors.generalHealth || {}}
            />

            <GroupedBooleanQuestions
              title="Have you ever had any of the following conditions?"
              questions={[
                { name: 'generalHealth.seizures', label: 'Seizures (fits)', required: true },
                { name: 'generalHealth.diabetes', label: 'Diabetes (Sugar Disease)', required: true },
                { name: 'generalHealth.allergicReactions', label: 'Allergic reactions that interfere with your breathing', required: true },
                { name: 'generalHealth.claustrophobia', label: 'Claustrophobia (fear of closed-in places)', required: true },
                { name: 'generalHealth.troubleSmellingOdors', label: 'Trouble smelling Odors', required: true }
              ]}
              control={control}
              errors={errors.generalHealth || {}}
            />

            <GroupedBooleanQuestions
              title="Have you ever had any of the following pulmonary problems?"
              questions={[
                { name: 'generalHealth.asbestosis', label: 'Asbestosis', required: true },
                { name: 'generalHealth.asthma', label: 'Asthma', required: true },
                { name: 'generalHealth.chronicBronchitis', label: 'Chronic bronchitis', required: true },
                { name: 'generalHealth.emphysema', label: 'Emphysema', required: true },
                { name: 'generalHealth.pneumonia', label: 'Pneumonia', required: true },
                { name: 'generalHealth.tuberculosis', label: 'Tuberculosis', required: true },
                { name: 'generalHealth.silicosis', label: 'Silicosis', required: true },
                { name: 'generalHealth.pneumothorax', label: 'Pneumothorax (collapsed lung)', required: true },
                { name: 'generalHealth.lungCancer', label: 'Lung cancer', required: true },
                { name: 'generalHealth.brokenRibs', label: 'Broken ribs', required: true },
                { name: 'generalHealth.chestInjuriesSurgeries', label: 'Any chest injuries or surgeries', required: true },
                { name: 'generalHealth.otherLungProblems', label: 'Any other lung problem that you have been told about', required: true }
              ]}
              control={control}
              errors={errors.generalHealth || {}}
            />
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
              Part 3 - Additional Questions
            </Typography>
            
            <GroupedBooleanQuestions
              title="1. Have you ever lost vision in either eye (temporarily or permanently)?"
              questions={[
                { name: 'additionalQuestions.lostVision', label: 'Have you ever lost vision in either eye (temporarily or permanently)?', required: true }
              ]}
              control={control}
              errors={errors.additionalQuestions || {}}
            />

            <GroupedBooleanQuestions
              title="2. Do you currently have any of these vision problems?"
              questions={[
                { name: 'additionalQuestions.needContactLenses', label: 'Need to wear contact lenses', required: true },
                { name: 'additionalQuestions.needGlasses', label: 'Need to wear glasses', required: true },
                { name: 'additionalQuestions.colorBlindness', label: 'Color blindness', required: true },
                { name: 'additionalQuestions.otherVisionProblems', label: 'Any other eye or vision problem', required: true }
              ]}
              control={control}
              errors={errors.additionalQuestions || {}}
            />

            <GroupedBooleanQuestions
              title="3. Have you ever had an injury to your ears, including a broken ear drum?"
              questions={[
                { name: 'additionalQuestions.earInjury', label: 'Have you ever had an injury to your ears, including a broken ear drum?', required: true }
              ]}
              control={control}
              errors={errors.additionalQuestions || {}}
            />

            <GroupedBooleanQuestions
              title="4. Do you currently have any of these hearing problems?"
              questions={[
                { name: 'additionalQuestions.difficultyHearing', label: 'Difficulty hearing', required: true },
                { name: 'additionalQuestions.needHearingAid', label: 'Need to wear hearing aid', required: true },
                { name: 'additionalQuestions.otherHearingProblems', label: 'Any other hearing or ear problem', required: true }
              ]}
              control={control}
              errors={errors.additionalQuestions || {}}
            />

            <GroupedBooleanQuestions
              title="5. Have you ever had a back injury?"
              questions={[
                { name: 'additionalQuestions.backInjury', label: 'Have you ever had a back injury?', required: true }
              ]}
              control={control}
              errors={errors.additionalQuestions || {}}
            />

            <GroupedBooleanQuestions
              title="6. Do you currently have any of the following musculoskeletal problems?"
              questions={[
                { name: 'additionalQuestions.weakness', label: 'Weakness in any of your arms, hands, legs, or feet?', required: true },
                { name: 'additionalQuestions.backPain', label: 'Back pain', required: true },
                { name: 'additionalQuestions.difficultyMovingArmsLegs', label: 'Difficulty fully moving your arms and legs', required: true },
                { name: 'additionalQuestions.painStiffnessWaist', label: 'Pain or stiffness when you lean forward or backward at the waist', required: true },
                { name: 'additionalQuestions.difficultyMovingHead', label: 'Difficulty fully moving your head up or down', required: true },
                { name: 'additionalQuestions.difficultyMovingHeadSide', label: 'Difficulty moving your head side to side', required: true },
                { name: 'additionalQuestions.difficultyBendingKnees', label: 'Difficulty bending at your knees', required: true },
                { name: 'additionalQuestions.difficultySquatting', label: 'Difficulty squatting to the ground', required: true },
                { name: 'additionalQuestions.difficultyClimbingStairs', label: 'Difficulty climbing a flight of stairs or a ladder carrying more than 25 lbs', required: true },
                { name: 'additionalQuestions.otherMusculoskeletalProblems', label: 'Any other muscle or skeletal problems that interferes with using a respirator', required: true }
              ]}
              control={control}
              errors={errors.additionalQuestions || {}}
            />
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
              Part 4 - PLHCP
            </Typography>
            
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
              Discretionary Questions (Please answer to the best of your ability)
            </Typography>
            
            <GroupedBooleanQuestions
              title="Work Environment"
              questions={[
                { 
                  name: 'plhcpQuestions.highAltitude', 
                  label: 'In your present job, are you working at high altitudes (over 5,000 feet) or in a place that has lower than normal amount of oxygen?', 
                  required: true 
                },
                { 
                  name: 'plhcpQuestions.hazardousExposure', 
                  label: 'Have you ever been exposed (at work or home) to hazardous solvents, hazardous airborne chemicals (such as, gases, fumes, or dust), or have you come into contact with hazardous chemicals?', 
                  required: true 
                }
              ]}
              control={control}
              errors={errors.plhcpQuestions || {}}
            />

            <GroupedBooleanQuestions
              title="Have you ever worked with any of the materials, or under any of the conditions listed below."
              questions={[
                { name: 'plhcpQuestions.asbestosExposure', label: 'Asbestos?', required: true },
                { name: 'plhcpQuestions.silicaExposure', label: 'Silica (for example, in sandblasting)?', required: true },
                { name: 'plhcpQuestions.tungstenCobaltExposure', label: 'Tungsten/cobalt (for example, grinding or welding this material)?', required: true },
                { name: 'plhcpQuestions.berylliumExposure', label: 'Beryllium?', required: true },
                { name: 'plhcpQuestions.aluminumExposure', label: 'Aluminum?', required: true },
                { name: 'plhcpQuestions.coalExposure', label: 'Coal (for example mining)?', required: true },
                { name: 'plhcpQuestions.ironExposure', label: 'Iron?', required: true },
                { name: 'plhcpQuestions.tinExposure', label: 'Tin?', required: true },
                { name: 'plhcpQuestions.dustyEnvironments', label: 'Dusty environments?', required: true },
                { name: 'plhcpQuestions.otherHazardousExposures', label: 'Any other hazardous exposures?', required: true }
              ]}
              control={control}
              errors={errors.plhcpQuestions || {}}
            />
          </Box>
        );

      case 4:
        return (
          <Box>
            <Typography variant="h5" gutterBottom sx={{ color: 'primary.main', fontWeight: 600 }}>
              Review & Submit
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              Please review your answers before submitting. Once submitted, this questionnaire cannot be changed.
            </Alert>
            
            <Typography variant="body1" paragraph>
              By submitting this questionnaire, you confirm that all information provided is accurate and complete to the best of your knowledge.
            </Typography>
          </Box>
        );

      default:
        return <Typography>Unknown step</Typography>;
    }
  };

  return (
    <Container maxWidth="md" sx={{ px: { xs: 2, sm: 3 } }}>
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
        <Box sx={{ my: { xs: 2, sm: 4 } }}>
          {/* Header */}
          <Paper sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            borderRadius: '12px',
            border: '1px solid #e0e0e0'
          }} elevation={3}>
            <Typography variant="h4" gutterBottom sx={{
              fontSize: { xs: '1.75rem', sm: '2.125rem' },
              fontWeight: 600
            }}>
              Respiratory Health Questionnaire
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" paragraph sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}>
              OSHA mandated respiratory health assessment questionnaire for employees who may be required to use respirators.
            </Typography>
          </Paper>

          {/* Stepper */}
          <Paper sx={{
            p: { xs: 2, sm: 3 },
            mb: 3,
            border: '1px solid #e0e0e0'
          }} elevation={1}>
            <Stepper
              activeStep={activeStep}
              alternativeLabel={!window.matchMedia('(max-width: 600px)').matches}
              orientation={window.matchMedia('(max-width: 600px)').matches ? 'vertical' : 'horizontal'}
              sx={{
                '& .MuiStepLabel-label': {
                  fontSize: { xs: '0.75rem', sm: '0.875rem' }
                }
              }}
            >
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Paper>

          {/* Form Content */}
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(handleSubmit)}>
              <Paper sx={{
                p: { xs: 2, sm: 3 },
                mb: 3,
                minHeight: '400px',
                border: '1px solid #e0e0e0'
              }} elevation={1}>
                <Box role="main" aria-label={`Step ${activeStep + 1} of ${steps.length}: ${steps[activeStep]}`}>
                  {renderStepContent(activeStep)}
                </Box>
              </Paper>

              {/* Navigation Buttons */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: 3,
                flexDirection: { xs: 'column', sm: 'row' },
                gap: { xs: 2, sm: 0 }
              }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  variant="contained"
                  aria-label="Go to previous step"
                  sx={{
                    bgcolor: '#17a2b8',
                    color: 'white',
                    '&:hover': { bgcolor: '#138496' },
                    '&:disabled': { bgcolor: 'grey.300', color: 'grey.600' },
                    px: { xs: 3, sm: 4 },
                    py: 1,
                    fontWeight: 600,
                    textTransform: 'uppercase',
                    order: { xs: 2, sm: 1 }
                  }}
                >
                  BACK
                </Button>

                {activeStep === steps.length - 1 ? (
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isSubmitting}
                    aria-label="Submit questionnaire"
                    sx={{
                      bgcolor: '#17a2b8',
                      color: 'white',
                      '&:hover': { bgcolor: '#138496' },
                      '&:disabled': { bgcolor: 'grey.300', color: 'grey.600' },
                      px: { xs: 3, sm: 4 },
                      py: 1,
                      fontWeight: 600,
                      textTransform: 'uppercase',
                      order: { xs: 1, sm: 2 }
                    }}
                  >
                    {isSubmitting ? 'Submitting...' : 'SUBMIT'}
                  </Button>
                ) : (
                  <Button
                    onClick={handleNext}
                    variant="contained"
                    aria-label="Go to next step"
                    sx={{
                      bgcolor: '#17a2b8',
                      color: 'white',
                      '&:hover': { bgcolor: '#138496' },
                      px: { xs: 3, sm: 4 },
                      py: 1,
                      fontWeight: 600,
                      textTransform: 'uppercase',
                      order: { xs: 1, sm: 2 }
                    }}
                  >
                    NEXT
                  </Button>
                )}
              </Box>
            </form>
          </FormProvider>
        </Box>
      </motion.div>
    </Container>
  );
};

export default RespiratoryQuestionnaireWizard;
