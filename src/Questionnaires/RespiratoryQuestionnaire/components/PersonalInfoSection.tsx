import React from 'react';
import { 
  Box, 
  TextField, 
  FormControl, 
  FormControlLabel, 
  Radio, 
  RadioGroup, 
  Typography, 
  Grid,
  InputAdornment,
  Paper
} from '@mui/material';
import { Controller } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface PersonalInfoSectionProps {
  control: any;
  errors: any;
  disabled?: boolean;
}

const PersonalInfoSection: React.FC<PersonalInfoSectionProps> = ({
  control,
  errors,
  disabled = false
}) => {
  return (
    <Paper elevation={1} sx={{ p: 3, mb: 3, border: '1px solid #e0e0e0' }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
        Employee Background Information
      </Typography>
      
      <Grid container spacing={3}>
        {/* First Name and Last Name */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="firstName"
            control={control}
            defaultValue=""
            rules={{ required: 'First name is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Firstname"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.firstName}
                helperText={errors.firstName?.message}
              />
            )}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Controller
            name="lastName"
            control={control}
            defaultValue=""
            rules={{ required: 'Last name is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Lastname"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.lastName}
                helperText={errors.lastName?.message}
              />
            )}
          />
        </Grid>

        {/* Email and Phone */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="email"
            control={control}
            defaultValue=""
            rules={{ 
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Email"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.email}
                helperText={errors.email?.message}
              />
            )}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Controller
            name="phoneNumber"
            control={control}
            defaultValue=""
            rules={{ required: 'Phone number is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Phone number"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.phoneNumber}
                helperText={errors.phoneNumber?.message}
              />
            )}
          />
        </Grid>

        {/* SSN and Birthdate */}
        <Grid item xs={12} sm={6}>
          <Controller
            name="ssn"
            control={control}
            defaultValue=""
            rules={{ required: 'SSN is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="SSN"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.ssn}
                helperText={errors.ssn?.message}
                placeholder="***********"
              />
            )}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Controller
              name="birthdate"
              control={control}
              defaultValue={null}
              rules={{ required: 'Birth date is required' }}
              render={({ field: { onChange, value, ...field } }) => (
                <DatePicker
                  {...field}
                  label="Birthdate"
                  value={value}
                  onChange={onChange}
                  disabled={disabled}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      error: !!errors.birthdate,
                      helperText: errors.birthdate?.message,
                      placeholder: "12/12/2002"
                    }
                  }}
                />
              )}
            />
          </LocalizationProvider>
        </Grid>

        {/* Gender */}
        <Grid item xs={12}>
          <FormControl component="fieldset" error={!!errors.gender}>
            <Typography variant="body1" gutterBottom sx={{ fontWeight: 500 }}>
              Gender
            </Typography>
            <Controller
              name="gender"
              control={control}
              defaultValue=""
              rules={{ required: 'Gender is required' }}
              render={({ field }) => (
                <RadioGroup
                  {...field}
                  row
                  aria-label="gender"
                  name="gender"
                >
                  <FormControlLabel 
                    value="male" 
                    control={<Radio />} 
                    label="Male" 
                    disabled={disabled}
                    sx={{ mr: 4 }}
                  />
                  <FormControlLabel 
                    value="female" 
                    control={<Radio />} 
                    label="Female" 
                    disabled={disabled} 
                  />
                </RadioGroup>
              )}
            />
          </FormControl>
        </Grid>

        {/* Height and Weight */}
        <Grid item xs={12} sm={4}>
          <Controller
            name="heightFeet"
            control={control}
            defaultValue=""
            rules={{ required: 'Height (feet) is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Height"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.heightFeet}
                helperText={errors.heightFeet?.message}
                InputProps={{
                  endAdornment: <InputAdornment position="end">ft.</InputAdornment>,
                }}
                type="number"
                placeholder="6"
              />
            )}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Controller
            name="heightInches"
            control={control}
            defaultValue=""
            rules={{ required: 'Height (inches) is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Height (inches)"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.heightInches}
                helperText={errors.heightInches?.message}
                InputProps={{
                  endAdornment: <InputAdornment position="end">in.</InputAdornment>,
                }}
                type="number"
                placeholder="0"
              />
            )}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Controller
            name="weight"
            control={control}
            defaultValue=""
            rules={{ required: 'Weight is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Weight"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.weight}
                helperText={errors.weight?.message}
                InputProps={{
                  endAdornment: <InputAdornment position="end">lbs</InputAdornment>,
                }}
                type="number"
                placeholder="234"
              />
            )}
          />
        </Grid>

        {/* Job Title */}
        <Grid item xs={12}>
          <Controller
            name="jobTitle"
            control={control}
            defaultValue=""
            rules={{ required: 'Job title is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Job Title"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.jobTitle}
                helperText={errors.jobTitle?.message}
                placeholder="test"
              />
            )}
          />
        </Grid>

        {/* Best Time to Call */}
        <Grid item xs={12}>
          <Controller
            name="bestTimeToCall"
            control={control}
            defaultValue=""
            rules={{ required: 'Best time to call is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="The best time to call you"
                variant="outlined"
                fullWidth
                required
                disabled={disabled}
                error={!!errors.bestTimeToCall}
                helperText={errors.bestTimeToCall?.message}
                placeholder="tes"
              />
            )}
          />
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PersonalInfoSection;
