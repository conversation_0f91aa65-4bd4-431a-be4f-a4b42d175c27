import React from 'react';
import { Box, FormControl, FormControlLabel, FormHelperText, Radio, RadioGroup, Typography, Paper } from '@mui/material';
import { Controller } from 'react-hook-form';

interface GroupedQuestion {
  name: string;
  label: string;
  required?: boolean;
}

interface GroupedBooleanQuestionsProps {
  title: string;
  description?: string;
  questions: GroupedQuestion[];
  control: any;
  errors: any;
  disabled?: boolean;
}

const GroupedBooleanQuestions: React.FC<GroupedBooleanQuestionsProps> = ({
  title,
  description,
  questions,
  control,
  errors,
  disabled = false
}) => {
  return (
    <Paper elevation={1} sx={{ p: 3, mb: 3, border: '1px solid #e0e0e0' }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
        {title}
      </Typography>
      {description && (
        <Typography variant="body2" color="text.secondary" paragraph>
          {description}
        </Typography>
      )}

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        {questions.map((question, index) => {
          const error = errors[question.name];
          const errorMessage = error?.message as string | undefined;

          return (
            <Box key={question.name}>
              <Box sx={{
                display: 'flex',
                alignItems: 'flex-start',
                minHeight: 48,
                py: 1,
                borderBottom: index < questions.length - 1 ? '1px solid #f0f0f0' : 'none'
              }}>
                <Box sx={{ flex: 1, mr: 3, pt: 1 }}>
                  <Typography variant="body2" sx={{ lineHeight: 1.4 }}>
                    {question.label}
                  </Typography>
                </Box>

                <FormControl component="fieldset" error={!!error} disabled={disabled}>
                  <Controller
                    name={question.name}
                    control={control}
                    defaultValue={false}
                    rules={{
                      required: question.required ? 'This field is required' : false
                    }}
                    render={({ field }) => (
                      <RadioGroup
                        {...field}
                        row
                        aria-label={question.label}
                        name={question.name}
                        value={field.value ? 'true' : 'false'}
                        onChange={(e) => field.onChange(e.target.value === 'true')}
                        sx={{ gap: 2 }}
                      >
                        <FormControlLabel
                          value="true"
                          control={<Radio color="primary" size="small" />}
                          label="Yes"
                          disabled={disabled}
                          sx={{
                            mr: 1,
                            '& .MuiFormControlLabel-label': { fontSize: '0.875rem' }
                          }}
                        />
                        <FormControlLabel
                          value="false"
                          control={<Radio color="primary" size="small" />}
                          label="No"
                          disabled={disabled}
                          sx={{
                            mr: 0,
                            '& .MuiFormControlLabel-label': { fontSize: '0.875rem' }
                          }}
                        />
                      </RadioGroup>
                    )}
                  />
                </FormControl>
              </Box>
              {errorMessage && (
                <Box sx={{ ml: 0, mt: 1 }}>
                  <Typography variant="caption" color="error" sx={{
                    bgcolor: '#d32f2f',
                    color: 'white',
                    px: 1,
                    py: 0.5,
                    borderRadius: 0.5,
                    fontSize: '0.75rem',
                    fontWeight: 500
                  }}>
                    Please select a value.
                  </Typography>
                </Box>
              )}
            </Box>
          );
        })}
      </Box>
    </Paper>
  );
};

export default GroupedBooleanQuestions;
