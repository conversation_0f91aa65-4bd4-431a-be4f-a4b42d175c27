import React from 'react';
import { Box, Typography } from '@mui/material';

interface ValidationErrorBoxProps {
  message: string;
}

const ValidationErrorBox: React.FC<ValidationErrorBoxProps> = ({ message }) => {
  return (
    <Box
      sx={{
        bgcolor: '#d32f2f',
        color: 'white',
        p: 1,
        borderRadius: 1,
        mb: 2,
        fontSize: '0.875rem',
        fontWeight: 500,
        textAlign: 'center'
      }}
    >
      {message}
    </Box>
  );
};

export default ValidationErrorBox;
