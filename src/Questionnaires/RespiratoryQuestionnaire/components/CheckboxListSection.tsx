import React from 'react';
import { 
  Box, 
  FormControl, 
  FormGroup, 
  FormControlLabel, 
  Checkbox, 
  FormHelperText, 
  FormLabel, 
  Typography,
  Paper,
  TextField
} from '@mui/material';
import { Controller, useWatch } from 'react-hook-form';

interface CheckboxListSectionProps {
  title: string;
  name: string;
  options: string[];
  control: any;
  errors: any;
  disabled?: boolean;
  required?: boolean;
  hasOtherOption?: boolean;
  otherOptionName?: string;
  otherOptionLabel?: string;
}

const CheckboxListSection: React.FC<CheckboxListSectionProps> = ({
  title,
  name,
  options,
  control,
  errors,
  disabled = false,
  required = false,
  hasOtherOption = false,
  otherOptionName,
  otherOptionLabel = "Please describe"
}) => {
  // Watch the checkbox values to show/hide the "Other" text field
  const selectedValues = useWatch({
    control,
    name,
    defaultValue: []
  });

  const showOtherField = hasOtherOption && selectedValues?.includes('Other');

  return (
    <Paper elevation={1} sx={{ p: 3, mb: 3, border: '1px solid #e0e0e0' }}>
      <FormControl component="fieldset" error={!!errors[name]} disabled={disabled} fullWidth>
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
          {title}
        </Typography>
        
        <Controller
          name={name}
          control={control}
          defaultValue={[]}
          rules={{
            required: required ? 'At least one option must be selected' : false,
            validate: (value) => {
              if (required && (!value || value.length === 0)) {
                return 'At least one option must be selected';
              }
              return true;
            }
          }}
          render={({ field }) => (
            <FormGroup sx={{ mt: 1 }}>
              {options.map((option) => (
                <FormControlLabel
                  key={option}
                  control={
                    <Checkbox
                      checked={field.value?.includes(option) || false}
                      onChange={(e) => {
                        const newValue = e.target.checked
                          ? [...(field.value || []), option]
                          : field.value?.filter((v: string) => v !== option) || [];
                        field.onChange(newValue);
                      }}
                      disabled={disabled}
                      color="primary"
                    />
                  }
                  label={option}
                  sx={{ 
                    mb: 0.5,
                    '& .MuiFormControlLabel-label': {
                      fontSize: '0.875rem'
                    }
                  }}
                />
              ))}
            </FormGroup>
          )}
        />
        {errors[name] && <FormHelperText>{errors[name]?.message}</FormHelperText>}
      </FormControl>

      {/* Other option text field */}
      {showOtherField && otherOptionName && (
        <Box sx={{ mt: 2, ml: 4 }}>
          <Controller
            name={otherOptionName}
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                {...field}
                label={otherOptionLabel}
                variant="outlined"
                fullWidth
                multiline
                rows={3}
                disabled={disabled}
                error={!!errors[otherOptionName]}
                helperText={errors[otherOptionName]?.message}
                sx={{ bgcolor: 'white' }}
              />
            )}
          />
        </Box>
      )}
    </Paper>
  );
};

export default CheckboxListSection;
