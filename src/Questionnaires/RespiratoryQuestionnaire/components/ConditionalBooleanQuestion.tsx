import React from 'react';
import { Box, FormControl, FormControlLabel, FormHelperText, Radio, RadioGroup, Typography } from '@mui/material';
import { Controller, useWatch } from 'react-hook-form';

interface ConditionalBooleanQuestionProps {
  name: string;
  control: any;
  label: string;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  defaultValue?: boolean;
  followUpQuestion?: {
    name: string;
    label: string;
    showWhen: boolean; // Show when main question is true or false
    required?: boolean;
  };
}

const ConditionalBooleanQuestion: React.FC<ConditionalBooleanQuestionProps> = ({
  name,
  control,
  label,
  required = false,
  error = false,
  helperText,
  disabled = false,
  defaultValue = false,
  followUpQuestion
}) => {
  // Watch the main question value to show/hide follow-up
  const mainValue = useWatch({
    control,
    name,
    defaultValue
  });

  const showFollowUp = followUpQuestion && mainValue === followUpQuestion.showWhen;

  return (
    <Box>
      {/* Main Question */}
      <FormControl component="fieldset" error={error} disabled={disabled} fullWidth>
        <Typography variant="body1" gutterBottom sx={{ fontWeight: 500 }}>
          {label}
        </Typography>
        <Controller
          name={name}
          control={control}
          defaultValue={defaultValue}
          rules={{
            required: required ? 'This field is required' : false
          }}
          render={({ field }) => (
            <RadioGroup
              {...field}
              row
              aria-label={label}
              name={name}
              value={field.value ? 'true' : 'false'}
              onChange={(e) => field.onChange(e.target.value === 'true')}
              sx={{ ml: 2 }}
            >
              <FormControlLabel 
                value="true" 
                control={<Radio color="primary" />} 
                label="Yes" 
                disabled={disabled}
                sx={{ mr: 4 }}
              />
              <FormControlLabel 
                value="false" 
                control={<Radio color="primary" />} 
                label="No" 
                disabled={disabled} 
              />
            </RadioGroup>
          )}
        />
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
      </FormControl>

      {/* Follow-up Question */}
      {showFollowUp && followUpQuestion && (
        <Box sx={{ ml: 4, mt: 2, p: 2, bgcolor: '#f8f9fa', borderRadius: 1, border: '1px solid #e9ecef' }}>
          <FormControl component="fieldset" fullWidth>
            <Typography variant="body2" gutterBottom sx={{ fontWeight: 500, color: 'text.primary' }}>
              {followUpQuestion.label}
            </Typography>
            <Controller
              name={followUpQuestion.name}
              control={control}
              defaultValue={false}
              rules={{
                required: followUpQuestion.required ? 'This field is required' : false
              }}
              render={({ field }) => (
                <RadioGroup
                  {...field}
                  row
                  aria-label={followUpQuestion.label}
                  name={followUpQuestion.name}
                  value={field.value ? 'true' : 'false'}
                  onChange={(e) => field.onChange(e.target.value === 'true')}
                  sx={{ ml: 2 }}
                >
                  <FormControlLabel
                    value="true"
                    control={<Radio color="primary" size="small" />}
                    label="Yes"
                    sx={{
                      mr: 4,
                      '& .MuiFormControlLabel-label': { fontSize: '0.875rem' }
                    }}
                  />
                  <FormControlLabel
                    value="false"
                    control={<Radio color="primary" size="small" />}
                    label="No"
                    sx={{
                      '& .MuiFormControlLabel-label': { fontSize: '0.875rem' }
                    }}
                  />
                </RadioGroup>
              )}
            />
          </FormControl>
        </Box>
      )}
    </Box>
  );
};

export default ConditionalBooleanQuestion;
