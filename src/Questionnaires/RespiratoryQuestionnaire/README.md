# Respiratory Health Questionnaire

A comprehensive OSHA-mandated respiratory health assessment questionnaire implementation for employees who may be required to use respirators.

## Overview

This module provides a complete implementation of the respiratory health questionnaire as required by OSHA standards. It includes a multi-step wizard interface, comprehensive data validation, and secure data storage.

## Template-Based Architecture

The respiratory questionnaire now follows the template-to-instance pattern used throughout the system:

### Template System
- **Template**: `respiratory-health-questionnaire-template` - The master template stored in the `questionnaireTemplates` collection
- **Instances**: Individual questionnaire instances created from the template and assigned to specific users
- **Wizard Type**: Uses `wizardType: 'respiratory'` to ensure the correct wizard interface is used

### Workflow
1. **Template Creation**: The respiratory questionnaire template is seeded into the database using the seeding script
2. **Instance Creation**: Administrators create instances from the template via the Templates page
3. **Assignment**: Instances are assigned to specific users (patients/employees)
4. **Completion**: Users complete assigned instances through the respiratory wizard interface
5. **Review**: Completed instances can be reviewed and managed like other questionnaires

### Components
- **RespiratoryQuestionnaireWizard**: Original static wizard (maintained for backward compatibility)
- **RespiratoryInstanceWizard**: New wizard for template-based instances
- **respiratoryInstanceService**: Service functions for managing template instances

## Features

### 🏥 OSHA Compliance
- Fully compliant with OSHA respiratory health assessment requirements
- Covers all mandatory and discretionary questions
- Structured according to official OSHA guidelines

### 📋 Multi-Part Structure
- **Part 1**: Employee Background Information (Mandatory)
- **Part 2**: General Health Information (Mandatory)
- **Part 3**: Additional Questions (Vision, Hearing, Musculoskeletal)
- **Part 4**: PLHCP Discretionary Questions
- **Final**: Agreement and Digital Signature

### 🎨 User Experience
- Step-by-step wizard interface
- Responsive design for all devices
- Real-time validation with clear error messages
- Progress tracking with visual stepper
- Conditional question logic
- Accessibility features (ARIA labels, keyboard navigation)

### 🔒 Data Security
- Secure Firebase Firestore storage
- User authentication integration
- Data encryption in transit and at rest
- HIPAA-compliant data handling

## Architecture

### Components

#### Core Components
- `RespiratoryQuestionnaireWizard` - Main wizard component
- `PersonalInfoSection` - Employee background information form
- `GroupedBooleanQuestions` - Health condition question groups
- `ConditionalBooleanQuestion` - Questions with follow-up logic
- `CheckboxListSection` - Multi-select option lists
- `ValidationErrorBox` - Error display component

#### Data Types
- `RespiratoryQuestionnaireData` - Complete questionnaire data structure
- `EmployeeBackgroundInfo` - Personal and job information
- `GeneralHealthInfo` - Medical history and conditions
- `AdditionalQuestions` - Vision, hearing, musculoskeletal health
- `PLHCPQuestions` - Occupational health and exposure history

#### Services
- `respiratoryQuestionnaireService` - Data persistence and retrieval
- Firebase Firestore integration
- User authentication handling

### File Structure

```
src/Questionnaires/RespiratoryQuestionnaire/
├── components/
│   ├── RespiratoryQuestionnaireWizard.tsx
│   ├── PersonalInfoSection.tsx
│   ├── GroupedBooleanQuestions.tsx
│   ├── ConditionalBooleanQuestion.tsx
│   ├── CheckboxListSection.tsx
│   └── ValidationErrorBox.tsx
├── data/
│   └── respiratoryQuestionnaireTemplate.ts
├── services/
│   └── respiratoryQuestionnaireService.ts
├── types/
│   └── RespiratoryQuestionnaireTypes.ts
├── tests/
│   └── RespiratoryQuestionnaireWizard.test.tsx
├── index.ts
└── README.md
```

## Usage

### Basic Implementation

```tsx
import { RespiratoryQuestionnaireWizard } from 'Questionnaires/RespiratoryQuestionnaire';

function App() {
  return <RespiratoryQuestionnaireWizard />;
}
```

### Routing

The questionnaire is accessible at `/trq/questionnaires/respiratory` and is integrated into the main questionnaire routing system.

### Data Submission

```tsx
import { submitRespiratoryQuestionnaire } from 'Questionnaires/RespiratoryQuestionnaire';

const handleSubmit = async (data: RespiratoryQuestionnaireData, userId: string) => {
  try {
    const submissionId = await submitRespiratoryQuestionnaire(data, userId);
    console.log('Submitted with ID:', submissionId);
  } catch (error) {
    console.error('Submission failed:', error);
  }
};
```

## Question Types

### Boolean Questions
- Yes/No questions with optional follow-up questions
- Conditional logic based on responses
- Validation for required fields

### Personal Information
- Text inputs with validation
- Date pickers for birthdate
- Radio buttons for gender selection
- Number inputs for height/weight

### Multi-Select Options
- Checkbox lists for respirator types
- Exposure risk selections
- "Other" option with text input

### Grouped Questions
- Related health conditions grouped together
- Consistent styling and layout
- Individual validation per question

## Validation

### Client-Side Validation
- Real-time validation using React Hook Form
- Required field validation
- Email format validation
- Custom validation rules per question type

### Error Handling
- Clear error messages
- Visual error indicators
- Accessibility-compliant error announcements
- Form submission prevention until valid

## Styling

### Design System
- Material-UI components
- Consistent color scheme (#17a2b8 primary)
- Responsive breakpoints
- Accessibility-compliant contrast ratios

### Layout
- Multi-column responsive grid
- Card-based sections
- Clear visual hierarchy
- Mobile-first design approach

## Testing

### Unit Tests
- Component rendering tests
- User interaction tests
- Validation logic tests
- Service integration tests

### Integration Tests
- Complete questionnaire flow
- Data submission verification
- Error handling scenarios

### Running Tests

```bash
npm test RespiratoryQuestionnaireWizard.test.tsx
```

## Accessibility

### WCAG Compliance
- ARIA labels for all form elements
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support

### Features
- Focus management
- Error announcements
- Progress indication
- Alternative text for icons

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

### Optimization
- Lazy loading of components
- Efficient re-rendering with React Hook Form
- Minimal bundle size impact
- Optimized Firebase queries

### Metrics
- First Contentful Paint: < 2s
- Time to Interactive: < 3s
- Bundle size impact: < 50KB gzipped

## Security

### Data Protection
- HTTPS-only transmission
- Firebase security rules
- User authentication required
- No sensitive data in client logs

### Compliance
- HIPAA-compliant data handling
- OSHA requirement adherence
- Privacy policy integration
- Audit trail maintenance

## Setup and Deployment

### Template Seeding

To set up the respiratory questionnaire template in your database:

```bash
# Run the seeding script
npx tsx scripts/seedRespiratoryTemplate.ts
```

This script will:
- Create the respiratory questionnaire template in the `questionnaireTemplates` collection
- Set the correct metadata including `wizardType: 'respiratory'`
- Enable administrators to create instances from the template

### Environment Setup
1. Configure Firebase project
2. Set up authentication
3. Deploy security rules
4. Configure environment variables

### Production Checklist
- [ ] Firebase security rules deployed
- [ ] Authentication configured
- [ ] SSL certificate installed
- [ ] Error monitoring enabled
- [ ] Performance monitoring active

## Support

For technical support or questions about the respiratory questionnaire implementation, please contact the development team or refer to the main project documentation.

## License

This implementation is part of the TRQ (The Respiratory Questionnaire) system and follows the project's licensing terms.
