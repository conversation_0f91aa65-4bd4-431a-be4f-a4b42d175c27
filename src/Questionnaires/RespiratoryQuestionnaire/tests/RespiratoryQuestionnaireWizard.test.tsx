/**
 * Tests for the Respiratory Questionnaire Wizard
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { SnackbarProvider } from 'notistack';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import RespiratoryQuestionnaireWizard from '../components/RespiratoryQuestionnaireWizard';

// Mock the auth context
jest.mock('../../../../Authentication/[contexts]/AuthContext', () => ({
  useAuth: () => ({
    userData: {
      uid: 'test-user-id',
      email: '<EMAIL>'
    }
  })
}));

// Mock the service
jest.mock('../services/respiratoryQuestionnaireService', () => ({
  submitRespiratoryQuestionnaire: jest.fn().mockResolvedValue('test-submission-id')
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  }
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <SnackbarProvider>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        {children}
      </LocalizationProvider>
    </SnackbarProvider>
  </BrowserRouter>
);

describe('RespiratoryQuestionnaireWizard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the questionnaire title and description', () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    expect(screen.getByText('Respiratory Health Questionnaire')).toBeInTheDocument();
    expect(screen.getByText(/OSHA mandated respiratory health assessment/)).toBeInTheDocument();
  });

  test('displays the stepper with correct steps', () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    expect(screen.getByText('Terms & Employee Info')).toBeInTheDocument();
    expect(screen.getByText('General Health')).toBeInTheDocument();
    expect(screen.getByText('Additional Questions')).toBeInTheDocument();
    expect(screen.getByText('PLHCP Questions')).toBeInTheDocument();
    expect(screen.getByText('Review & Submit')).toBeInTheDocument();
  });

  test('shows terms agreement checkbox on first step', () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    expect(screen.getByText(/I have read and agree to the/)).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
  });

  test('shows personal information fields on first step', () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/Firstname/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Lastname/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Phone number/)).toBeInTheDocument();
  });

  test('navigation buttons work correctly', async () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    const backButton = screen.getByText('BACK');
    const nextButton = screen.getByText('NEXT');

    // Back button should be disabled on first step
    expect(backButton).toBeDisabled();

    // Fill required fields to enable next
    const termsCheckbox = screen.getByRole('checkbox');
    fireEvent.click(termsCheckbox);

    const firstNameInput = screen.getByLabelText(/Firstname/);
    fireEvent.change(firstNameInput, { target: { value: 'John' } });

    const lastNameInput = screen.getByLabelText(/Lastname/);
    fireEvent.change(lastNameInput, { target: { value: 'Doe' } });

    const emailInput = screen.getByLabelText(/Email/);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    // Next button should be enabled after filling required fields
    expect(nextButton).not.toBeDisabled();
  });

  test('validates required fields', async () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    const nextButton = screen.getByText('NEXT');
    
    // Try to proceed without filling required fields
    fireEvent.click(nextButton);

    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/Please select a value/)).toBeInTheDocument();
    });
  });

  test('displays different content for each step', async () => {
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    // Fill required fields on first step
    const termsCheckbox = screen.getByRole('checkbox');
    fireEvent.click(termsCheckbox);

    // Fill personal info (simplified for test)
    const firstNameInput = screen.getByLabelText(/Firstname/);
    fireEvent.change(firstNameInput, { target: { value: 'John' } });

    const lastNameInput = screen.getByLabelText(/Lastname/);
    fireEvent.change(lastNameInput, { target: { value: 'Doe' } });

    const emailInput = screen.getByLabelText(/Email/);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    const phoneInput = screen.getByLabelText(/Phone number/);
    fireEvent.change(phoneInput, { target: { value: '555-1234' } });

    const ssnInput = screen.getByLabelText(/SSN/);
    fireEvent.change(ssnInput, { target: { value: '***********' } });

    const jobTitleInput = screen.getByLabelText(/Job Title/);
    fireEvent.change(jobTitleInput, { target: { value: 'Test Job' } });

    const bestTimeInput = screen.getByLabelText(/best time to call/);
    fireEvent.change(bestTimeInput, { target: { value: 'Morning' } });

    // Navigate to next step
    const nextButton = screen.getByText('NEXT');
    fireEvent.click(nextButton);

    // Should show step 2 content
    await waitFor(() => {
      expect(screen.getByText('Part 2 - (Mandatory)')).toBeInTheDocument();
      expect(screen.getByText('General Health Information')).toBeInTheDocument();
    });
  });
});

// Integration test for the complete flow
describe('RespiratoryQuestionnaireWizard Integration', () => {
  test('can complete the entire questionnaire flow', async () => {
    const { submitRespiratoryQuestionnaire } = require('../services/respiratoryQuestionnaireService');
    
    render(
      <TestWrapper>
        <RespiratoryQuestionnaireWizard />
      </TestWrapper>
    );

    // This would be a more comprehensive test that goes through all steps
    // For now, we'll just verify the component renders without errors
    expect(screen.getByText('Respiratory Health Questionnaire')).toBeInTheDocument();
  });
});
