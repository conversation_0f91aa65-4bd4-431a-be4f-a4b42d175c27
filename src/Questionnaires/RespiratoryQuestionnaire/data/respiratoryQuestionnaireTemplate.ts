/**
 * Respiratory Health Questionnaire Template
 * Based on OSHA mandated respiratory questionnaire requirements
 */

import { QuestionType } from '../../[types]/QuestionType';
import { Question } from '../../[types]/Question';
import { RESPIRATOR_TYPES, EXPOSURE_RISKS } from '../types/RespiratoryQuestionnaireTypes';

// Part 1: Employee Background Information (Mandatory)
export const employeeBackgroundQuestions: Question[] = [
  {
    id: 'agreedToTerms',
    text: 'I have read and agree to the Terms and Conditions, Privacy Policy, and Business Associate Agreement',
    description: 'If you are utilizing this questionnaire, you attest that you can read and that you understand this does not constitute as a patient provider relationship.',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 1
  },
  {
    id: 'firstName',
    text: 'First Name',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 2
  },
  {
    id: 'lastName',
    text: 'Last Name',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 3
  },
  {
    id: 'email',
    text: 'Email',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 4
  },
  {
    id: 'phoneNumber',
    text: 'Phone Number',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 5
  },
  {
    id: 'ssn',
    text: 'SSN',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 6
  },
  {
    id: 'birthdate',
    text: 'Birth Date',
    questionType: QuestionType.DATE,
    isOptional: false,
    order: 7
  },
  {
    id: 'gender',
    text: 'Gender',
    questionType: QuestionType.RADIO,
    options: ['Male', 'Female'],
    isOptional: false,
    order: 8
  },
  {
    id: 'heightFeet',
    text: 'Height (feet)',
    questionType: QuestionType.NUMBER,
    isOptional: false,
    order: 9
  },
  {
    id: 'heightInches',
    text: 'Height (inches)',
    questionType: QuestionType.NUMBER,
    isOptional: false,
    order: 10
  },
  {
    id: 'weight',
    text: 'Weight (lbs)',
    questionType: QuestionType.NUMBER,
    isOptional: false,
    order: 11
  },
  {
    id: 'jobTitle',
    text: 'Job Title',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 12
  },
  {
    id: 'bestTimeToCall',
    text: 'The best time to call you',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 13
  },
  {
    id: 'previouslyWornRespirator',
    text: 'Have you previously worn a respirator?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 14
  },
  {
    id: 'respiratorTypes',
    text: 'Type of Respirators the employee may be using include:',
    questionType: QuestionType.CHECKBOX,
    options: RESPIRATOR_TYPES,
    isOptional: true,
    order: 15
  },
  {
    id: 'respiratorTypesOther',
    text: 'Please describe other respirator types',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 16
  },
  {
    id: 'exposureRisks',
    text: 'Exposure Risks May Include',
    questionType: QuestionType.CHECKBOX,
    options: EXPOSURE_RISKS,
    isOptional: true,
    order: 17
  }
];

// Part 2: General Health Information (Mandatory)
export const generalHealthQuestions: Question[] = [
  {
    id: 'currentSmoker',
    text: 'Do you currently smoke tobacco, or have you smoked tobacco in the last month?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 1
  },
  {
    id: 'seizures',
    text: 'Have you ever had any of the following conditions? - Seizures (fits)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 2
  },
  {
    id: 'diabetes',
    text: 'Have you ever had any of the following conditions? - Diabetes (Sugar Disease)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 3
  },
  {
    id: 'allergicReactions',
    text: 'Have you ever had any of the following conditions? - Allergic reactions that interfere with your breathing',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 4
  },
  {
    id: 'claustrophobia',
    text: 'Have you ever had any of the following conditions? - Claustrophobia (fear of closed-in places)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 5
  },
  {
    id: 'troubleSmellingOdors',
    text: 'Have you ever had any of the following conditions? - Trouble smelling Odors',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 6
  },
  {
    id: 'asbestosis',
    text: 'Have you ever had any of the following pulmonary problems? - Asbestosis',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 7
  },
  {
    id: 'asthma',
    text: 'Have you ever had any of the following pulmonary problems? - Asthma',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 8
  },
  {
    id: 'asthmaInhaler3Years',
    text: 'Have you used an asthma medication in the last 3 years, including an albuterol inhaler?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 9
  },
  {
    id: 'asthmaEmergency3Years',
    text: 'Have you been to the emergency room due to asthma in the last 3 years?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 10
  },
  {
    id: 'chronicBronchitis',
    text: 'Have you ever had any of the following pulmonary problems? - Chronic bronchitis',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 11
  },
  {
    id: 'chronicBronchitis3Years',
    text: 'Have you had chronic bronchitis in the last 3 years?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 12
  },
  {
    id: 'emphysema',
    text: 'Have you ever had any of the following pulmonary problems? - Emphysema',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 13
  },
  {
    id: 'pneumonia',
    text: 'Have you ever had any of the following pulmonary problems? - Pneumonia',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 14
  },
  {
    id: 'pneumonia3Years',
    text: 'Have you had pneumonia in the last 3 years?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 15
  },
  {
    id: 'tuberculosis',
    text: 'Have you ever had any of the following pulmonary problems? - Tuberculosis',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 16
  },
  {
    id: 'silicosis',
    text: 'Have you ever had any of the following pulmonary problems? - Silicosis',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 17
  },
  {
    id: 'pneumothorax',
    text: 'Have you ever had any of the following pulmonary problems? - Pneumothorax (collapsed lung)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 18
  },
  {
    id: 'pneumothorax3Years',
    text: 'Have you had Pneumothorax in the last 3 years?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 19
  },
  {
    id: 'lungCancer',
    text: 'Have you ever had any of the following pulmonary problems? - Lung cancer',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 20
  },
  {
    id: 'brokenRibs',
    text: 'Have you ever had any of the following pulmonary problems? - Broken ribs',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 21
  },
  {
    id: 'currentRibPain',
    text: 'Do you currently experience rib pain?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 22
  },
  {
    id: 'chestInjuriesSurgeries',
    text: 'Have you ever had any of the following pulmonary problems? - Any chest injuries or surgeries',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 23
  },
  {
    id: 'chestInjuriesSurgeries3Years',
    text: 'Were your chest injuries or surgeries in the last 3 years?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 24
  },
  {
    id: 'otherLungProblems',
    text: 'Have you ever had any of the following pulmonary problems? - Any other lung problem that you have been told about',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 25
  },
  {
    id: 'otherLungProblems3Years',
    text: 'Did the lung problems you had been told about occur in the last 3 years?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 26
  },
  // Pulmonary Symptoms
  {
    id: 'shortnessOfBreath',
    text: 'Do you currently have any of the following symptoms of pulmonary or lung illness? - Shortness of breath',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 27
  },
  {
    id: 'shortnessOfBreathWalking',
    text: 'Shortness of breath when walking fast on level ground or walking up a slight hill or incline',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 28
  },
  {
    id: 'shortnessOfBreathOrdinaryPace',
    text: 'Shortness of breath when walking with other people at an ordinary pace on level ground',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 29
  },
  {
    id: 'stopForBreath',
    text: 'Have to stop for breath when walking at your own pace on level ground',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 30
  },
  {
    id: 'shortnessOfBreathDressing',
    text: 'Shortness of breath when washing or dressing yourself',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 31
  },
  {
    id: 'shortnessOfBreathJob',
    text: 'Shortness of breath that interferes with your job',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 32
  },
  {
    id: 'coughingPhlegm',
    text: 'Coughing that produces Phlegm (thick sputum)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 33
  },
  {
    id: 'coughingPhlegm3Months',
    text: 'Has the cough producing phlegm lasted for at least 3 months?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 34
  },
  {
    id: 'coughingMorning',
    text: 'Coughing that wakes you early in the morning',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 35
  },
  {
    id: 'coughingMorning3Months',
    text: 'Has the cough that wakes you early in the morning lasted for a least 3 months?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 36
  },
  {
    id: 'coughingLyingDown',
    text: 'Coughing that occurs mostly when you are lying down',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 37
  },
  {
    id: 'coughingBlood',
    text: 'Coughing up blood in the last month',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 38
  },
  {
    id: 'wheezing',
    text: 'Wheezing',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 39
  },
  {
    id: 'wheezingJob',
    text: 'Wheezing that interferes with your job',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 40
  },
  {
    id: 'chestPainBreathing',
    text: 'Chest pain when you breathe deeply',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 41
  },
  {
    id: 'otherLungSymptoms',
    text: 'Any other symptoms that you think may be related to lung problems',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 42
  },
  {
    id: 'otherLungSymptomsDescription',
    text: 'Please describe other lung symptoms',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 43
  },
  // Cardiovascular Problems
  {
    id: 'heartAttack',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Heart attack',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 44
  },
  {
    id: 'stroke',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Stroke',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 45
  },
  {
    id: 'angina',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Angina',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 46
  },
  {
    id: 'heartFailure',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Heart failure',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 47
  },
  {
    id: 'legSwelling',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Swelling in your legs or feet (not caused by walking)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 48
  },
  {
    id: 'heartArrhythmia',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Heart arrhythmia (heart beating irregularly)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 49
  },
  {
    id: 'highBloodPressure',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - High blood pressure',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 50
  },
  {
    id: 'bloodPressureNormal',
    text: 'Is your blood pressure within normal range while taking medication (140/90)',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 51
  },
  {
    id: 'otherHeartProblems',
    text: 'Have you ever had any of the following cardiovascular or heart problems? - Any other problems that you have been told about',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 52
  },
  {
    id: 'otherHeartProblemsDescription',
    text: 'Please describe other heart problems',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 53
  },
  // Cardiovascular Symptoms
  {
    id: 'chestPainTightness',
    text: 'Have you ever had any of the following Cardiovascular or heart symptoms? - Frequent pain or tightness in your chest',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 54
  },
  {
    id: 'chestPainActivity',
    text: 'Pain or tightness in your chest during physical activity',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 55
  },
  {
    id: 'chestPainJob',
    text: 'Pain or tightness in your chest that interferes with your job',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 56
  },
  {
    id: 'heartSkipping',
    text: 'In the past 2 years, have you noticed your heart skipping or missing beat',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 57
  },
  {
    id: 'heartburn',
    text: 'Heartburn or indigestion that isn\'t related to eating',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 58
  },
  {
    id: 'otherHeartSymptoms',
    text: 'Any other symptoms that you think may be related to heart or circulation problems',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 59
  },
  {
    id: 'otherHeartSymptomsDescription',
    text: 'Please describe other heart symptoms',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 60
  },
  // Medications
  {
    id: 'breathingMedication',
    text: 'Do you currently take medication for any of the following problems? - Breathing or lung problems',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 61
  },
  {
    id: 'breathingMedicationDetails',
    text: 'Please list medication, dosage, and name and location of physician you see regarding breathing problems',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 62
  },
  {
    id: 'heartMedication',
    text: 'Do you currently take medication for any of the following problems? - Heart trouble',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 63
  },
  {
    id: 'heartMedicationDetails',
    text: 'Please list medication, dosage, and name and location of physician you see regarding heart trouble',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 64
  },
  {
    id: 'bloodPressureMedication',
    text: 'Do you currently take medication for any of the following problems? - High blood pressure',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 65
  },
  {
    id: 'bloodPressureMedicationDetails',
    text: 'Please list medication, dosage, and name and location of physician you see regarding high blood pressure',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 66
  },
  {
    id: 'bloodPressureMedicationNormal',
    text: 'Is your blood pressure within normal range while taking medication (140/90)?',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 67
  },
  {
    id: 'seizureMedication',
    text: 'Do you currently take medication for any of the following problems? - Seizures',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 68
  },
  // Respirator Issues
  {
    id: 'eyeIrritation',
    text: 'If you have used a respirator, have you ever had any of the following problems? - Eye irritation',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 69
  },
  {
    id: 'eyeIrritationCurrent',
    text: 'Do you currently have this issue while wearing a respirator? - Eye irritation',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 70
  },
  {
    id: 'skinAllergies',
    text: 'If you have used a respirator, have you ever had any of the following problems? - Skin allergies or rashes',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 71
  },
  {
    id: 'skinAllergiesCurrent',
    text: 'Do you currently have this issue while wearing a respirator? - Skin allergies or rashes',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 72
  },
  {
    id: 'anxiety',
    text: 'If you have used a respirator, have you ever had any of the following problems? - Anxiety',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 73
  },
  {
    id: 'anxietyCurrent',
    text: 'Do you currently have this issue while wearing a respirator? - Anxiety',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 74
  },
  {
    id: 'weaknessRespirator',
    text: 'If you have used a respirator, have you ever had any of the following problems? - General weakness or fatigue',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 75
  },
  {
    id: 'weaknessRespiratorCurrent',
    text: 'Do you currently have this issue while wearing a respirator? - General weakness or fatigue',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 76
  },
  {
    id: 'otherRespiratorProblems',
    text: 'If you have used a respirator, have you ever had any of the following problems? - Any other problems that interfere with your use of a respirator?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 77
  },
  {
    id: 'otherRespiratorProblemsCurrent',
    text: 'Do you currently have this issue while wearing a respirator? - Other problems',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 78
  },
  {
    id: 'otherRespiratorProblemsDescription',
    text: 'Please describe other respirator problems',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 79
  }
];

// Part 3: Additional Questions
export const additionalQuestions: Question[] = [
  {
    id: 'lostVision',
    text: 'Have you ever lost vision in either eye (temporarily or permanently)?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 1
  },
  {
    id: 'needContactLenses',
    text: 'Do you currently have any of these vision problems? - Need to wear contact lenses',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 2
  },
  {
    id: 'needGlasses',
    text: 'Do you currently have any of these vision problems? - Need to wear glasses',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 3
  },
  {
    id: 'colorBlindness',
    text: 'Do you currently have any of these vision problems? - Color blindness',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 4
  },
  {
    id: 'otherVisionProblems',
    text: 'Do you currently have any of these vision problems? - Any other eye or vision problem',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 5
  },
  {
    id: 'earInjury',
    text: 'Have you ever had an injury to your ears, including a broken ear drum?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 6
  },
  {
    id: 'difficultyHearing',
    text: 'Do you currently have any of these hearing problems? - Difficulty hearing',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 7
  },
  {
    id: 'needHearingAid',
    text: 'Do you currently have any of these hearing problems? - Need to wear hearing aid',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 8
  },
  {
    id: 'otherHearingProblems',
    text: 'Do you currently have any of these hearing problems? - Any other hearing or ear problem',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 9
  },
  {
    id: 'backInjury',
    text: 'Have you ever had a back injury?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 10
  },
  {
    id: 'weakness',
    text: 'Do you currently have any of the following musculoskeletal problems? - Weakness in any of your arms, hands, legs, or feet?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 11
  },
  {
    id: 'backPain',
    text: 'Do you currently have any of the following musculoskeletal problems? - Back pain',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 12
  },
  {
    id: 'difficultyMovingArmsLegs',
    text: 'Do you currently have any of the following musculoskeletal problems? - Difficulty fully moving your arms and legs',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 13
  },
  {
    id: 'painStiffnessWaist',
    text: 'Do you currently have any of the following musculoskeletal problems? - Pain or stiffness when you lean forward or backward at the waist',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 14
  },
  {
    id: 'difficultyMovingHead',
    text: 'Do you currently have any of the following musculoskeletal problems? - Difficulty fully moving your head up or down',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 15
  },
  {
    id: 'difficultyMovingHeadSide',
    text: 'Do you currently have any of the following musculoskeletal problems? - Difficulty moving your head side to side',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 16
  },
  {
    id: 'difficultyBendingKnees',
    text: 'Do you currently have any of the following musculoskeletal problems? - Difficulty bending at your knees',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 17
  },
  {
    id: 'difficultySquatting',
    text: 'Do you currently have any of the following musculoskeletal problems? - Difficulty squatting to the ground',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 18
  },
  {
    id: 'difficultyClimbingStairs',
    text: 'Do you currently have any of the following musculoskeletal problems? - Difficulty climbing a flight of stairs or a ladder carrying more than 25 lbs',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 19
  },
  {
    id: 'otherMusculoskeletalProblems',
    text: 'Do you currently have any of the following musculoskeletal problems? - Any other muscle or skeletal problems that interferes with using a respirator',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 20
  }
];

// Part 4: PLHCP Questions
export const plhcpQuestions: Question[] = [
  {
    id: 'highAltitude',
    text: 'In your present job, are you working at high altitudes (over 5,000 feet) or in a place that has lower than normal amount of oxygen?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 1
  },
  {
    id: 'hazardousExposure',
    text: 'Have you ever been exposed (at work or home) to hazardous solvents, hazardous airborne chemicals (such as, gases, fumes, or dust), or have you come into contact with hazardous chemicals?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 2
  },
  {
    id: 'asbestosExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Asbestos?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 3
  },
  {
    id: 'silicaExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Silica (for example, in sandblasting)?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 4
  },
  {
    id: 'tungstenCobaltExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Tungsten/cobalt (for example, grinding or welding this material)?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 5
  },
  {
    id: 'berylliumExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Beryllium?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 6
  },
  {
    id: 'aluminumExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Aluminum?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 7
  },
  {
    id: 'coalExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Coal (for example mining)?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 8
  },
  {
    id: 'ironExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Iron?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 9
  },
  {
    id: 'tinExposure',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Tin?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 10
  },
  {
    id: 'dustyEnvironments',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Dusty environments?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 11
  },
  {
    id: 'otherHazardousExposures',
    text: 'Have you ever worked with any of the materials, or under any of the conditions listed below - Any other hazardous exposures?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 12
  },
  {
    id: 'secondJobs',
    text: 'List any second jobs or side businesses you have:',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 13
  },
  {
    id: 'previousOccupations',
    text: 'List your previous occupations',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 14
  },
  {
    id: 'hobbies',
    text: 'List your current and previous hobbies',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 15
  },
  {
    id: 'militaryService',
    text: 'Have you been in the military services?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 16
  },
  {
    id: 'hazmatTeam',
    text: 'Have you ever worked on a HAZMAT team?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 17
  },
  {
    id: 'otherMedications',
    text: 'Other than medications for breathing and lung problems, heart trouble, blood pressure, and seizures mentioned earlier in this questionnaire, are you taking any other medications for any reason (including over-the-counter medications)?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 18
  },
  {
    id: 'otherMedicationsNames',
    text: 'Name the medications if you know them',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 19
  },
  // Respirator Equipment
  {
    id: 'hepaFilters',
    text: 'Will you be using any of the following items with your respirator(s)? - HEPA Filters',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 20
  },
  {
    id: 'canisters',
    text: 'Will you be using any of the following items with your respirator(s)? - Canisters (for example, gas masks)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 21
  },
  {
    id: 'cartridges',
    text: 'Will you be using any of the following items with your respirator(s)? - Cartridges',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 22
  },
  // Usage Frequency
  {
    id: 'escapeOnly',
    text: 'How often are you expected to use the respirator(s)? - Escape only (no rescue)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 23
  },
  {
    id: 'emergencyRescue',
    text: 'How often are you expected to use the respirator(s)? - Emergency rescue only',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 24
  },
  {
    id: 'lessThan5Hours',
    text: 'How often are you expected to use the respirator(s)? - Less than 5 hours per week',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 25
  },
  {
    id: 'lessThan2Hours',
    text: 'How often are you expected to use the respirator(s)? - Less than 2 hours per day',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 26
  },
  {
    id: 'twoTo4Hours',
    text: 'How often are you expected to use the respirator(s)? - 2 to 4 hours per day',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 27
  },
  {
    id: 'over4Hours',
    text: 'How often are you expected to use the respirator(s)? - Over 4 hours per day',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 28
  },
  // Work Effort
  {
    id: 'lightWork',
    text: 'During the period you are using the respirator(s), is your work effort: Light (Less than 200 kcal per hour)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 29
  },
  {
    id: 'lightWorkDuration',
    text: 'If "yes" how long does this period last during the average shift (hrs : mins)',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 30
  },
  {
    id: 'moderateWork',
    text: 'During the period you are using the respirator(s), is your work effort: Moderate (200 to 350 kcal per hour)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 31
  },
  {
    id: 'moderateWorkDuration',
    text: 'If "yes" how long does this period last during the average shift (hrs : mins)',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 32
  },
  {
    id: 'heavyWork',
    text: 'During the period you are using the respirator(s), is your work effort: Heavy (above 350 kcal per hour)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 33
  },
  {
    id: 'heavyWorkDuration',
    text: 'If "yes" how long does this period last during the average shift (hrs : mins)',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 34
  },
  // Work Conditions
  {
    id: 'protectiveClothing',
    text: 'Will you be wearing protective clothing and/or equipment (other than the respirator) when you are using the respirator?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 35
  },
  {
    id: 'protectiveClothingDescription',
    text: 'Describe the protective clothing and/or equipment',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 36
  },
  {
    id: 'hotConditions',
    text: 'Will you be working under hot conditions (temperature exceeding 77 degrees)',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 37
  },
  {
    id: 'humidConditions',
    text: 'Will you be working under humid conditions',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 38
  },
  {
    id: 'workDescription',
    text: 'Describe the work you will be doing while using your respirator(s)',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 39
  },
  {
    id: 'hazardousConditions',
    text: 'Describe any special or hazardous conditions you might encounter when you are using your respirator(s) (for example, confined spaces, life - threatening gases)',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 40
  },
  // Toxic Substances
  {
    id: 'toxicSubstance1',
    text: 'Provide the following information, if you know it, for each toxic substance that you will be exposed to when you are using your respirator(s) - Name of the first toxic substance',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 41
  },
  {
    id: 'toxicSubstance1Exposure',
    text: 'Estimated maximum exposure level per shift - First toxic substance',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 42
  },
  {
    id: 'toxicSubstance1Duration',
    text: 'Duration of exposure per shift - First toxic substance',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 43
  },
  {
    id: 'toxicSubstance2',
    text: 'Name of second toxic substance',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 44
  },
  {
    id: 'toxicSubstance2Exposure',
    text: 'Estimated maximum exposure level per shift - Second toxic substance',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 45
  },
  {
    id: 'toxicSubstance2Duration',
    text: 'Duration of exposure per shift - Second toxic substance',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 46
  },
  {
    id: 'otherToxicSubstances',
    text: 'The name of any other toxic substances that you will be exposed to while using your respirator',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 47
  },
  {
    id: 'specialResponsibilities',
    text: 'Describe any special responsibilities you will have while using your respirator(s) that may affect the safety and well being of others (for example, rescue, security)',
    questionType: QuestionType.TEXTAREA,
    isOptional: true,
    order: 48
  },
  // Cancer History
  {
    id: 'cancer',
    text: 'Have you ever been diagnosed with cancer?',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 49
  },
  {
    id: 'boneCancer',
    text: 'Have you ever been diagnosed with cancer? - Bone',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 50
  },
  {
    id: 'brainCancer',
    text: 'Have you ever been diagnosed with cancer? - Brain',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 51
  },
  {
    id: 'breastCancer',
    text: 'Have you ever been diagnosed with cancer? - Breast',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 52
  },
  {
    id: 'colonCancer',
    text: 'Have you ever been diagnosed with cancer? - Colon',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 53
  },
  {
    id: 'lungCancerHistory',
    text: 'Have you ever been diagnosed with cancer? - Lung',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 54
  },
  {
    id: 'pancreaticCancer',
    text: 'Have you ever been diagnosed with cancer? - Pancreatic',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 55
  },
  {
    id: 'prostateCancer',
    text: 'Have you ever been diagnosed with cancer? - Prostate',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 56
  },
  {
    id: 'skinCancer',
    text: 'Have you ever been diagnosed with cancer? - Skin',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 57
  },
  {
    id: 'smallIntestineCancer',
    text: 'Have you ever been diagnosed with cancer? - Small Intestine',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 58
  },
  {
    id: 'stomachCancer',
    text: 'Have you ever been diagnosed with cancer? - Stomach',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 59
  },
  {
    id: 'throatCancer',
    text: 'Have you ever been diagnosed with cancer? - Throat',
    questionType: QuestionType.BOOLEAN,
    isOptional: true,
    order: 60
  },
  {
    id: 'otherCancer',
    text: 'Have you ever been diagnosed with cancer? - Other (please specify)',
    questionType: QuestionType.TEXT,
    isOptional: true,
    order: 61
  }
];

// Final Agreement Questions
export const finalAgreementQuestions: Question[] = [
  {
    id: 'finalAgreement',
    text: 'By signing this, you assert that you have read this questionnaire and answered the questions truthfully to the best of your knowledge and ability. You understand that providing intentional false answers or other false information to any of the questions in the questionnaire may lead to a condition that could be detrimental to your health and well being. You authorize the doctor to review your answers and to send a compliance report to your employer.',
    description: 'I agree with the statement above.',
    questionType: QuestionType.BOOLEAN,
    isOptional: false,
    order: 1
  },
  {
    id: 'signatureName',
    text: 'Please write your name here:',
    questionType: QuestionType.TEXT,
    isOptional: false,
    order: 2
  }
];

// Legacy template structure for backward compatibility
export const respiratoryQuestionnaireTemplate = {
  id: 'respiratory-health-questionnaire',
  name: 'Respiratory Health Questionnaire',
  title: 'Respiratory Health Questionnaire',
  description: 'OSHA mandated respiratory health assessment questionnaire for employees who may be required to use respirators.',
  isPublished: true,
  metadata: {
    category: 'Respiratory Health',
    estimatedTime: 30,
    parts: 4,
    mandatory: true
  },
  parts: [
    {
      id: 'part1',
      title: 'Part 1 - Employee Background Information (Mandatory)',
      description: 'Basic employee information and respirator usage history',
      questions: employeeBackgroundQuestions
    },
    {
      id: 'part2',
      title: 'Part 2 - General Health Information (Mandatory)',
      description: 'Medical history and current health conditions',
      questions: generalHealthQuestions
    },
    {
      id: 'part3',
      title: 'Part 3 - Additional Questions',
      description: 'Vision, hearing, and musculoskeletal health assessment',
      questions: additionalQuestions
    },
    {
      id: 'part4',
      title: 'Part 4 - PLHCP (Discretionary Questions)',
      description: 'Occupational health and exposure history',
      questions: plhcpQuestions
    },
    {
      id: 'final',
      title: 'Final Agreement and Signature',
      description: 'Review and sign the questionnaire',
      questions: finalAgreementQuestions
    }
  ],
  // Flattened questions for compatibility with existing system
  questions: [
    ...employeeBackgroundQuestions,
    ...generalHealthQuestions,
    ...additionalQuestions,
    ...plhcpQuestions,
    ...finalAgreementQuestions
  ]
};

// Proper QuestionnaireTemplate for the template system
export const respiratoryQuestionnaireTemplateData = {
  id: 'respiratory-health-questionnaire-template',
  name: 'Respiratory Health Questionnaire',
  title: 'Respiratory Health Questionnaire',
  createdBy: 'system',
  description: 'OSHA mandated respiratory health assessment questionnaire for employees who may be required to use respirators.',
  isPublished: true,
  metadata: {
    category: 'Respiratory Health',
    estimatedTime: 30,
    wizardType: 'respiratory', // Indicates this should use the respiratory wizard
    parts: 4,
    mandatory: true
  },
  questions: [
    ...employeeBackgroundQuestions,
    ...generalHealthQuestions,
    ...additionalQuestions,
    ...plhcpQuestions,
    ...finalAgreementQuestions
  ],
  version: 1,
  category: 'Respiratory Health',
  tags: ['respiratory', 'health', 'osha', 'mandatory', 'employee-screening'],
  // Store the parts structure in metadata for the respiratory wizard
  respiratoryParts: [
    {
      id: 'part1',
      title: 'Part 1 - Employee Background Information (Mandatory)',
      description: 'Basic employee information and respirator usage history',
      questions: employeeBackgroundQuestions
    },
    {
      id: 'part2',
      title: 'Part 2 - General Health Information (Mandatory)',
      description: 'Medical history and current health conditions',
      questions: generalHealthQuestions
    },
    {
      id: 'part3',
      title: 'Part 3 - Additional Questions',
      description: 'Vision, hearing, and musculoskeletal health assessment',
      questions: additionalQuestions
    },
    {
      id: 'part4',
      title: 'Part 4 - PLHCP (Discretionary Questions)',
      description: 'Occupational health and exposure history',
      questions: plhcpQuestions
    },
    {
      id: 'final',
      title: 'Final Agreement and Signature',
      description: 'Review and sign the questionnaire',
      questions: finalAgreementQuestions
    }
  ]
};

// Re-export constants for use in components
export { RESPIRATOR_TYPES, EXPOSURE_RISKS };
