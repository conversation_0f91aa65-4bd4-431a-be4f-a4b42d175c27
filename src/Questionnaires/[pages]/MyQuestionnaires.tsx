import React, { useState, useEffect, useTransition } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { Timestamp } from 'firebase/firestore';

// material-ui
import { Grid } from '@mui/material';
import Stack from '@mui/material/Stack';
import { DataGrid, GridColDef, GridRenderCellParams, GridToolbarContainer } from '@mui/x-data-grid';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import CircularProgress from '@mui/material/CircularProgress';

// project imports
import MainCard from '../../[components]/cards/MainCard';
import SecondaryAction from '../../[components]/cards/CardSecondaryAction';
import { gridSpacing } from '../../[constants]/gridSpacing';
import useDataGrid from '../../[hooks]/useDataGrid';
import { getQuestionnairesByPatientUid } from '../[services]/questionnaireService';
import { getCurrentUserData } from '../../Users/<USER>/userService';
import { Questionnaire, QuestionnaireStatus } from '../[types]/Questionnaire';
import usePermission from '../../RBAC/[hooks]/usePermission';

// icons
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AssignmentReturnedIcon from '@mui/icons-material/AssignmentReturned';
import QuizIcon from '@mui/icons-material/Quiz';
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn';
import ROUTES from 'Routing/appRoutes';

const formatTimestamp = (timestamp: Timestamp | undefined): string => {
  if (!timestamp) return '';
  return timestamp.toDate().toLocaleDateString();
};

const MyQuestionnaires = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const dataGridStyles = useDataGrid();
  // Use permissions for future enhancements
  usePermission();
  const [isPending, startTransition] = useTransition();

  const [questionnaires, setQuestionnaires] = useState<Questionnaire[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Fetch user data and their questionnaires on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get current user data
        const userData = await getCurrentUserData();
        if (userData && userData.uid) {
          // Get questionnaires for the current patient by Firebase Auth UID
          const questionnairesData = await getQuestionnairesByPatientUid(userData.uid);
          setQuestionnaires(questionnairesData);
        } else {
          console.error('Could not retrieve current user UID');
        }
      } catch (error) {
        console.error('Error fetching user questionnaires:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle view action
  const handleView = (id: string) => {
    startTransition(() => {
      navigate(ROUTES.QUESTIONNAIRES.DETAILS(id));
    });
  };

  // Handle start or continue questionnaire
  const handleStartQuestionnaire = (questionnaire: Questionnaire, status: string) => {
    // Navigate to questionnaire wizard to start or continue, passing the full questionnaire object
    startTransition(() => {
      navigate(`/trq/questionnaires/wizard/${questionnaire.id}`, { state: { questionnaire } });
    });
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  // Filter questionnaires based on search term and status
  const filteredQuestionnaires = questionnaires.filter((questionnaire) => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch =
      questionnaire.title.toLowerCase().includes(searchLower) ||
      (questionnaire.description && questionnaire.description.toLowerCase().includes(searchLower));
    const matchesStatus = statusFilter === 'all' || questionnaire.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Custom toolbar with search and filter
  const CustomToolbar = () => {
    return (
      <GridToolbarContainer sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider', flexDirection: 'column' }}>
        {/* Top row: Title */}
        <Stack direction="row" spacing={2} alignItems="center" width="100%" justifyContent="space-between" sx={{ mb: 2 }}>
          <Typography variant="h4">{intl.formatMessage({ id: 'my-questionnaires' }) || 'My Questionnaires'}</Typography>
        </Stack>

        {/* Bottom row: Search and Filters */}
        <Stack direction="row" spacing={1} alignItems="center" sx={{ justifyContent: 'flex-end', width: '100%' }}>
          <TextField
            size="small"
            variant="outlined"
            placeholder={intl.formatMessage({ id: 'search' }) || 'Search...'}
            value={searchTerm}
            onChange={handleSearchChange}
            data-testid="questionnaire-search-input"
            InputProps={{
              startAdornment: <SearchIcon fontSize="small" sx={{ mr: 0.5 }} />
            }}
          />

          <FormControl size="small" sx={{ minWidth: 130 }}>
            <InputLabel>{intl.formatMessage({ id: 'status' }) || 'Status'}</InputLabel>
            <Select
              value={statusFilter}
              onChange={handleStatusFilterChange}
              label={intl.formatMessage({ id: 'status' }) || 'Status'}
              data-testid="questionnaire-status-filter"
            >
              <MenuItem value="all">{intl.formatMessage({ id: 'all' }) || 'All'}</MenuItem>
              <MenuItem value="assigned">{intl.formatMessage({ id: 'assigned' }) || 'Assigned'}</MenuItem>
              <MenuItem value="in-progress">{intl.formatMessage({ id: 'in-progress' }) || 'In Progress'}</MenuItem>
              <MenuItem value="completed">{intl.formatMessage({ id: 'completed' }) || 'Completed'}</MenuItem>
              <MenuItem value="reviewed">{intl.formatMessage({ id: 'reviewed' }) || 'Reviewed'}</MenuItem>
            </Select>
          </FormControl>
        </Stack>
      </GridToolbarContainer>
    );
  };

  // Define columns for the data grid
  const columns: GridColDef[] = [
    {
      field: 'title',
      headerName: intl.formatMessage({ id: 'title' }) || 'Title',
      flex: 1.5,
      minWidth: 200,
      headerAlign: 'left',
      align: 'left'
    },
    {
      field: 'status',
      headerName: intl.formatMessage({ id: 'status' }) || 'Status',
      flex: 1,
      minWidth: 120,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        const statusColors: { [key: string]: 'default' | 'warning' | 'success' | 'info' | 'primary' | 'secondary' | 'error' } = {
          assigned: 'secondary',
          'in-progress': 'warning',
          completed: 'success',
          reviewed: 'info'
        };
        const status = (params.value as string) || 'assigned';
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%', height: '100%' }}>
            <Chip
              label={status ? (intl.formatMessage({ id: status }) || status) : 'Unknown'}
              color={statusColors[status] || 'default'}
              size="small"
              variant="filled"
            />
          </Box>
        );
      }
    },
    {
      field: 'createdAt',
      headerName: intl.formatMessage({ id: 'created-at' }) || 'Created At',
      flex: 1,
      minWidth: 130,
      headerAlign: 'center',
      align: 'center',
      valueFormatter: (params: any) => {
        if (!params?.value) return '';
        return formatTimestamp(params.value as Timestamp);
      }
    },
    {
      field: 'assignedAt',
      headerName: intl.formatMessage({ id: 'assigned-at' }) || 'Assigned At',
      flex: 1,
      minWidth: 160,
      headerAlign: 'center',
      align: 'center',
      valueGetter: (params: any) => params?.row?.assignedTo?.assignedAt ?? '',
      valueFormatter: (params: any) => {
        if (!params?.value) return '';
        return formatTimestamp(params.value as Timestamp);
      }
    },
    {
      field: 'startedAt',
      headerName: intl.formatMessage({ id: 'started-at' }) || 'Started At',
      flex: 1,
      minWidth: 130,
      headerAlign: 'center',
      align: 'center',
      valueFormatter: (params: any) => {
        if (!params?.value) return '';
        return formatTimestamp(params.value as Timestamp);
      }
    },
    {
      field: 'completedAt',
      headerName: intl.formatMessage({ id: 'completed-at' }) || 'Completed At',
      flex: 1,
      minWidth: 130,
      headerAlign: 'center',
      align: 'center',
      valueFormatter: (params: any) => {
        if (!params?.value) return '';
        return formatTimestamp(params.value as Timestamp);
      }
    },
    {
      field: 'actions',
      headerName: intl.formatMessage({ id: 'actions' }) || 'Actions',
      flex: 1,
      minWidth: 130,
      headerAlign: 'center',
      align: 'center',
      sortable: false,
      renderCell: (params: GridRenderCellParams<Questionnaire>) => {
        if (!params.row || !params.row.id) {
          return null;
        }

        const status = params.row.status;
        const questionnaire = params.row;

        return (
          <Stack direction="row" spacing={1}>
            {/* View button for all questionnaires */}
            <Button
              variant="outlined"
              size="small"
              color="info"
              onClick={(e) => {
                e.stopPropagation();
                handleView(params.id.toString());
              }}
              startIcon={<VisibilityIcon />}
            >
              {intl.formatMessage({ id: 'view' }) || 'View'}
            </Button>

            {/* Start/Continue button for assigned or in-progress questionnaires */}
            {(status === QuestionnaireStatus.Assigned || status === QuestionnaireStatus.InProgress) && (
              <Button
                variant="contained"
                size="small"
                color="primary"
                onClick={(e) => {
                  e.stopPropagation();
                  handleStartQuestionnaire(questionnaire, status);
                }}
                startIcon={status === QuestionnaireStatus.Assigned ? <QuizIcon /> : <AssignmentReturnedIcon />}
              >
                {status === QuestionnaireStatus.Assigned
                  ? intl.formatMessage({ id: 'start' }) || 'Start'
                  : intl.formatMessage({ id: 'continue' }) || 'Continue'}
              </Button>
            )}

            {/* Show completed indicator for completed/reviewed questionnaires */}
            {(status === QuestionnaireStatus.Completed || status === QuestionnaireStatus.Reviewed) && (
              <Button variant="outlined" size="small" color="success" disabled startIcon={<AssignmentTurnedInIcon />}>
                {intl.formatMessage({ id: 'completed' }) || 'Completed'}
              </Button>
            )}
          </Stack>
        );
      }
    }
  ];

  if (loading || isPending) {
    return (
      <Grid container spacing={gridSpacing}>
        <Grid item xs={12}>
          <MainCard content={false} title="" secondary={<SecondaryAction link="https://mui.com/x/react-data-grid/" />}>
            <Box sx={{ width: '100%' }} data-testid="my-questionnaires-data-grid">
              <Box display="flex" justifyContent="center" alignItems="center" sx={{ py: 5 }}>
                <CircularProgress />
              </Box>
            </Box>
          </MainCard>
        </Grid>
      </Grid>
    );
  }

  return (
    <Grid container spacing={gridSpacing}>
      <Grid item xs={12}>
        <MainCard content={false} title="" secondary={<SecondaryAction link="https://mui.com/x/react-data-grid/" />}>
          <Box sx={{ width: '100%' }} data-testid="my-questionnaires-data-grid">
            <DataGrid
              rows={filteredQuestionnaires}
              columns={columns}
              autoHeight
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 10
                  }
                },
                sorting: {
                  sortModel: [{ field: 'assignedAt', sort: 'desc' }]
                }
              }}
              pageSizeOptions={[5, 10, 25, 50]}
              slots={{
                toolbar: CustomToolbar
              }}
              sx={{
                ...dataGridStyles,
                border: 'none',
                '& .MuiDataGrid-toolbarContainer': {
                  padding: 2,
                  borderBottom: '1px solid',
                  borderColor: 'divider'
                },
                '& .MuiDataGrid-cell': {
                  py: 1.5,
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                  display: 'flex',
                  alignItems: 'center'
                },
                '& .MuiDataGrid-columnHeaders': {
                  borderBottom: '1px solid',
                  borderColor: 'divider'
                },
                '& .MuiDataGrid-row': {
                  cursor: 'pointer'
                },
                '& .MuiDataGrid-footerContainer': {
                  padding: 1,
                  borderTop: '1px solid',
                  borderColor: 'divider'
                }
              }}
              onRowClick={(params) => {
                handleView(params.id.toString());
              }}
            />
          </Box>
        </MainCard>
      </Grid>
    </Grid>
  );
};

export default MyQuestionnaires;
