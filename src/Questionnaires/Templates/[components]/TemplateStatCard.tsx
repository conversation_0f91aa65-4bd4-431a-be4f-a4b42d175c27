import { styled, useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import MainCard from '[components]/cards/MainCard';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import CategoryIcon from '@mui/icons-material/Category';
import Chip from '@mui/material/Chip';

interface TemplateStatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  subtext?: string;
  color?: string;
  up?: boolean;
  category?: string;
  coverImage?: string;
  isPublished?: boolean;
}

// --- SVG Background Variants (Consistent gradient patterns) ---
const svgBackgrounds = [
  // Gradient Pattern 1 - Top-left to bottom-right
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.15"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.05"/></linearGradient></defs><rect width="400" height="220" fill="url(%23grad1)"/><circle cx="350" cy="50" r="80" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.08"/></svg>')`,
  // Gradient Pattern 2 - Top-right to bottom-left
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="grad2" x1="100%" y1="0%" x2="0%" y2="100%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.15"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.05"/></linearGradient></defs><rect width="400" height="220" fill="url(%23grad2)"/><circle cx="50" cy="50" r="80" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.08"/></svg>')`,
  // Gradient Pattern 3 - Bottom gradient with circle
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="grad3" x1="0%" y1="100%" x2="100%" y2="0%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.15"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.05"/></linearGradient></defs><rect width="400" height="220" fill="url(%23grad3)"/><circle cx="200" cy="170" r="80" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.08"/></svg>')`,
  // Gradient Pattern 4 - Radial gradient from center
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="grad4" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.15"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.05"/></radialGradient></defs><rect width="400" height="220" fill="url(%23grad4)"/></svg>')`
];

// --- Updated CardWrapper with dynamic SVG background ---
const CardWrapper = styled(MainCard)<{ bgVariant?: number; hasCoverImage?: boolean }>(
  ({ theme, bgVariant = 0, hasCoverImage = false }) => ({
    backgroundColor: theme.palette.primary.dark,
    color: theme.palette.primary.light,
    overflow: 'hidden',
    position: 'relative',
    height: 220,
    backgroundImage: !hasCoverImage ? svgBackgrounds[bgVariant % svgBackgrounds.length](theme) : 'none',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    '&:after': {
      content: '""',
      position: 'absolute',
      width: 120,
      height: 120,
      background: `radial-gradient(circle, ${theme.palette.primary[100]} 0%, transparent 70%)`,
      borderRadius: '50%',
      top: -40,
      right: -40,
      opacity: hasCoverImage ? 0 : 0.1
    },
    '&:before': {
      content: '""',
      position: 'absolute',
      width: 100,
      height: 100,
      background: `radial-gradient(circle, ${theme.palette.primary[200]} 0%, transparent 70%)`,
      borderRadius: '50%',
      bottom: -30,
      left: -30,
      opacity: hasCoverImage ? 0 : 0.08
    }
  })
);

// Cover image style with overlay
const CoverImageWrapper = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  zIndex: 0,
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(0, 0, 0, 0.6)',
    zIndex: 1
  }
});

const CoverImage = styled('img')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  display: 'block'
});

export default function TemplateStatCard({
  title,
  value,
  icon,
  subtext,
  color,
  up,
  bgVariant = 0, // new prop for background variant
  category,
  coverImage,
  isPublished
}: TemplateStatCardProps & { bgVariant?: number }) {
  const theme = useTheme();
  const hasCoverImage = !!coverImage;

  // Format the subtext to consistently show the published status
  const formattedSubtext = () => {
    let statusText = '';

    // Extract version if it exists in the subtext
    const versionMatch = subtext?.match(/v\d+/) || [''];
    const versionText = versionMatch[0];

    // Add the draft/published status
    if (isPublished !== undefined) {
      statusText = isPublished ? 'Published' : 'Draft';
    }

    // Combine version and status
    if (versionText && statusText) {
      return `${versionText} | ${statusText}`;
    } else if (versionText) {
      return versionText;
    } else if (statusText) {
      return statusText;
    }

    // Fall back to original subtext if we couldn't extract meaningful information
    return subtext || '';
  };

  return (
    <CardWrapper border={false} content={false} bgVariant={bgVariant} hasCoverImage={hasCoverImage}>
      {hasCoverImage && (
        <CoverImageWrapper>
          <CoverImage src={coverImage} alt={title} />
        </CoverImageWrapper>
      )}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          height: '100%',
          justifyContent: 'center',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}
      >
        <Avatar
          variant="rounded"
          sx={{
            bgcolor: color || theme.palette.primary[700],
            color: '#fff',
            width: 56,
            height: 56,
            mb: 2,
            boxShadow: 2,
            fontSize: 32,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {icon || <InsertDriveFileOutlinedIcon fontSize="large" />}
        </Avatar>
        <Typography variant="h3" sx={{ color: '#fff', fontWeight: 700, mb: 0.5, lineHeight: 1.2 }}>
          {value}
          {up && (
            <Box
              component="span"
              sx={{
                ml: 1,
                color: theme.palette.success.light,
                fontSize: 18,
                verticalAlign: 'middle',
                fontWeight: 700
              }}
            >
              ↑
            </Box>
          )}
        </Typography>
        <Typography variant="subtitle1" sx={{ color: 'primary.light', fontWeight: 500, mb: 0.5 }}>
          {title}
        </Typography>
        {category && (
          <Chip
            icon={<CategoryIcon />}
            label={category}
            size="small"
            sx={{ mt: 0.5, mb: 0.5, bgcolor: 'background.paper', color: 'text.primary', fontWeight: 500 }}
            variant="outlined"
          />
        )}
        {(subtext || isPublished !== undefined) && (
          <Typography variant="caption" sx={{ color: 'text.secondary', opacity: 0.72 }}>
            {formattedSubtext()}
          </Typography>
        )}
      </Box>
    </CardWrapper>
  );
}
