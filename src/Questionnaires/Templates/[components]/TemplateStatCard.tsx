import { styled, useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import MainCard from '[components]/cards/MainCard';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import CategoryIcon from '@mui/icons-material/Category';
import Chip from '@mui/material/Chip';

interface TemplateStatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  subtext?: string;
  color?: string;
  up?: boolean;
  category?: string;
  coverImage?: string;
  isPublished?: boolean;
}

// --- SVG Background Variants (Modern geometric patterns) ---
const svgBackgrounds = [
  // Geometric Wave Pattern
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="wave1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.2"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.secondary.light)}" stop-opacity="0.1"/></linearGradient></defs><path d="M0,220 C100,180 200,200 300,160 C350,140 400,120 400,100 L400,220 Z" fill="url(%23wave1)"/><circle cx="320" cy="60" r="40" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.12"/><circle cx="80" cy="180" r="25" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.1"/></svg>')`,
  // Hexagonal Pattern
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="hex1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.18"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.08"/></linearGradient></defs><polygon points="300,40 320,60 320,100 300,120 280,100 280,60" fill="url(%23hex1)"/><polygon points="100,120 120,140 120,180 100,200 80,180 80,140" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.12"/><rect x="0" y="0" width="400" height="220" fill="${encodeURIComponent(theme.palette.primary[50])}" fill-opacity="0.05"/></svg>')`,
  // Flowing Lines Pattern
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="flow1" x1="0%" y1="50%" x2="100%" y2="50%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.2"/><stop offset="50%" stop-color="${encodeURIComponent(theme.palette.secondary.light)}" stop-opacity="0.15"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.1"/></linearGradient></defs><path d="M0,110 Q100,80 200,110 T400,110" stroke="url(%23flow1)" stroke-width="60" fill="none" opacity="0.6"/><ellipse cx="350" cy="50" rx="30" ry="15" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.12"/><ellipse cx="50" cy="170" rx="25" ry="20" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.1"/></svg>')`,
  // Abstract Shapes Pattern
  (theme: any) =>
    `url('data:image/svg+xml;utf8,<svg width="400" height="220" viewBox="0 0 400 220" fill="none" xmlns="http://www.w3.org/2000/svg"><defs><radialGradient id="abstract1" cx="30%" cy="30%" r="70%"><stop offset="0%" stop-color="${encodeURIComponent(theme.palette.primary[100])}" stop-opacity="0.2"/><stop offset="100%" stop-color="${encodeURIComponent(theme.palette.primary[200])}" stop-opacity="0.05"/></radialGradient></defs><ellipse cx="120" cy="80" rx="80" ry="60" fill="url(%23abstract1)" transform="rotate(-15 120 80)"/><path d="M250,160 Q300,140 350,160 Q340,180 290,190 Q260,185 250,160" fill="${encodeURIComponent(theme.palette.secondary.light)}" fill-opacity="0.12"/><circle cx="320" cy="60" r="20" fill="${encodeURIComponent(theme.palette.primary[100])}" fill-opacity="0.15"/></svg>')`
];

// --- Updated CardWrapper with dynamic SVG background ---
const CardWrapper = styled(MainCard)<{ bgVariant?: number; hasCoverImage?: boolean }>(
  ({ theme, bgVariant = 0, hasCoverImage = false }) => ({
    backgroundColor: theme.palette.primary.dark,
    color: theme.palette.primary.light,
    overflow: 'hidden',
    position: 'relative',
    height: 220,
    backgroundImage: !hasCoverImage ? svgBackgrounds[bgVariant % svgBackgrounds.length](theme) : 'none',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    '&:after': {
      content: '""',
      position: 'absolute',
      width: 200,
      height: 200,
      background: `conic-gradient(from 45deg, ${theme.palette.primary[100]}, transparent, ${theme.palette.secondary.light}, transparent)`,
      borderRadius: '50%',
      top: -100,
      right: -100,
      opacity: hasCoverImage ? 0 : 0.08,
      animation: 'rotate 20s linear infinite'
    },
    '&:before': {
      content: '""',
      position: 'absolute',
      width: 150,
      height: 150,
      background: `linear-gradient(135deg, ${theme.palette.primary[200]} 0%, transparent 50%, ${theme.palette.secondary.light} 100%)`,
      borderRadius: '50%',
      bottom: -75,
      left: -75,
      opacity: hasCoverImage ? 0 : 0.06
    },
    '@keyframes rotate': {
      '0%': { transform: 'rotate(0deg)' },
      '100%': { transform: 'rotate(360deg)' }
    }
  })
);

// Cover image style with overlay
const CoverImageWrapper = styled(Box)({
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  overflow: 'hidden',
  zIndex: 0,
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(0, 0, 0, 0.6)',
    zIndex: 1
  }
});

const CoverImage = styled('img')({
  width: '100%',
  height: '100%',
  objectFit: 'cover',
  display: 'block'
});

export default function TemplateStatCard({
  title,
  value,
  icon,
  subtext,
  color,
  up,
  bgVariant = 0, // new prop for background variant
  category,
  coverImage,
  isPublished
}: TemplateStatCardProps & { bgVariant?: number }) {
  const theme = useTheme();
  const hasCoverImage = !!coverImage;

  // Format the subtext to consistently show the published status
  const formattedSubtext = () => {
    let statusText = '';

    // Extract version if it exists in the subtext
    const versionMatch = subtext?.match(/v\d+/) || [''];
    const versionText = versionMatch[0];

    // Add the draft/published status
    if (isPublished !== undefined) {
      statusText = isPublished ? 'Published' : 'Draft';
    }

    // Combine version and status
    if (versionText && statusText) {
      return `${versionText} | ${statusText}`;
    } else if (versionText) {
      return versionText;
    } else if (statusText) {
      return statusText;
    }

    // Fall back to original subtext if we couldn't extract meaningful information
    return subtext || '';
  };

  return (
    <CardWrapper border={false} content={false} bgVariant={bgVariant} hasCoverImage={hasCoverImage}>
      {hasCoverImage && (
        <CoverImageWrapper>
          <CoverImage src={coverImage} alt={title} />
        </CoverImageWrapper>
      )}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          height: '100%',
          justifyContent: 'center',
          textAlign: 'center',
          position: 'relative',
          zIndex: 2
        }}
      >
        <Avatar
          variant="rounded"
          sx={{
            bgcolor: color || theme.palette.primary[700],
            color: '#fff',
            width: 56,
            height: 56,
            mb: 2,
            boxShadow: 2,
            fontSize: 32,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {icon || <InsertDriveFileOutlinedIcon fontSize="large" />}
        </Avatar>
        <Typography variant="h3" sx={{ color: '#fff', fontWeight: 700, mb: 0.5, lineHeight: 1.2 }}>
          {value}
          {up && (
            <Box
              component="span"
              sx={{
                ml: 1,
                color: theme.palette.success.light,
                fontSize: 18,
                verticalAlign: 'middle',
                fontWeight: 700
              }}
            >
              ↑
            </Box>
          )}
        </Typography>
        <Typography variant="subtitle1" sx={{ color: 'primary.light', fontWeight: 500, mb: 0.5 }}>
          {title}
        </Typography>
        {category && (
          <Chip
            icon={<CategoryIcon />}
            label={category}
            size="small"
            sx={{ mt: 0.5, mb: 0.5, bgcolor: 'background.paper', color: 'text.primary', fontWeight: 500 }}
            variant="outlined"
          />
        )}
        {(subtext || isPublished !== undefined) && (
          <Typography variant="caption" sx={{ color: 'text.secondary', opacity: 0.72 }}>
            {formattedSubtext()}
          </Typography>
        )}
      </Box>
    </CardWrapper>
  );
}
