import { test, expect } from '@playwright/test';

test.describe('Doctor - Patient Management CRUD', () => {
  const testPatient = {
    firstName: 'Doctor',
    lastName: 'Patient',
    email: '<EMAIL>',
    dateOfBirth: '1985-05-15',
    gender: 'Female',
    phone: '******-1111',
    medicalHistory: 'No significant medical history'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Doctor
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/doctors/home');
  });

  test('CREATE - Doctor can add new patient', async ({ page }) => {
    // Navigate to my patients
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');

    // Click Add Patient button
    await page.click('[data-testid="add-patient-button"]');
    await page.waitForSelector('[data-testid="add-patient-dialog"]');

    // Fill patient form
    await page.fill('[data-testid="first-name-input"]', testPatient.firstName);
    await page.fill('[data-testid="last-name-input"]', testPatient.lastName);
    await page.fill('[data-testid="email-input"]', testPatient.email);
    await page.fill('[data-testid="date-of-birth-input"]', testPatient.dateOfBirth);
    
    // Select gender
    await page.click('[data-testid="gender-select"]');
    await page.click(`[data-value="${testPatient.gender}"]`);

    await page.fill('[data-testid="phone-input"]', testPatient.phone);
    await page.fill('[data-testid="medical-history-input"]', testPatient.medicalHistory);

    // Submit form
    await page.click('[data-testid="submit-patient-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Patient added successfully');

    // Verify patient appears in my patients list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testPatient.email}`)).toBeVisible();
  });

  test('READ - Doctor can view patient details', async ({ page }) => {
    // Navigate to my patients
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');

    // Search for test patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Click on patient to view details
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);
    
    // Verify patient details page
    await page.waitForURL('**/patients/*/details');
    await expect(page.locator('[data-testid="patient-details-name"]')).toContainText(`${testPatient.firstName} ${testPatient.lastName}`);
    await expect(page.locator('[data-testid="patient-details-email"]')).toContainText(testPatient.email);
    await expect(page.locator('[data-testid="patient-details-medical-history"]')).toContainText(testPatient.medicalHistory);
  });

  test('UPDATE - Doctor can update patient medical information', async ({ page }) => {
    // Navigate to my patients
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');

    // Search for test patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/patients/*/edit');

    // Update medical history
    const updatedMedicalHistory = 'Updated: Patient reports occasional headaches';
    await page.fill('[data-testid="medical-history-input"]', updatedMedicalHistory);

    // Save changes
    await page.click('[data-testid="save-patient-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Patient updated successfully');

    // Verify changes in patient details
    await expect(page.locator('[data-testid="patient-details-medical-history"]')).toContainText(updatedMedicalHistory);
  });

  test('Doctor can assign questionnaire to patient', async ({ page }) => {
    // Navigate to patient details
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to questionnaires tab
    await page.click('[data-testid="questionnaires-tab"]');

    // Assign questionnaire
    await page.click('[data-testid="assign-questionnaire-button"]');
    await page.waitForSelector('[data-testid="questionnaire-select"]');
    
    // Select a questionnaire template
    await page.click('[data-testid="questionnaire-select"]');
    await page.click('[data-value="respiratory-health-template"]');

    // Set due date
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 3);
    await page.fill('[data-testid="due-date-input"]', futureDate.toISOString().split('T')[0]);

    // Confirm assignment
    await page.click('[data-testid="confirm-assignment-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="assigned-questionnaires-list"]')).toContainText('Respiratory Health');
  });

  test('Doctor can create compliance report for patient', async ({ page }) => {
    // Navigate to patient details
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to compliance reports tab
    await page.click('[data-testid="compliance-reports-tab"]');

    // Create new compliance report
    await page.click('[data-testid="create-report-button"]');
    await page.waitForSelector('[data-testid="compliance-report-form"]');

    // Fill report details
    await page.fill('[data-testid="medical-evaluation"]', 'Patient appears healthy and fit for work');
    
    // Select respirator clearance
    await page.click('[data-testid="respirator-clearance-select"]');
    await page.click('[data-value="fully_cleared"]');

    await page.fill('[data-testid="clearance-description"]', 'Patient cleared for all respirator types');

    // Save report
    await page.click('[data-testid="save-report-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="compliance-reports-list"]')).toContainText('Medical Evaluation');
  });

  test('Doctor can view patient questionnaire responses', async ({ page }) => {
    // Navigate to patient details
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to questionnaires tab
    await page.click('[data-testid="questionnaires-tab"]');

    // Look for completed questionnaires
    const completedQuestionnaire = page.locator('[data-testid="questionnaire-status-completed"]').first();
    if (await completedQuestionnaire.isVisible()) {
      // Click to review responses
      await page.click('[data-testid="review-responses-button"]');
      
      // Verify review page
      await page.waitForURL('**/questionnaires/review/*');
      await expect(page.locator('[data-testid="questionnaire-responses"]')).toBeVisible();
      await expect(page.locator('[data-testid="patient-info"]')).toBeVisible();
    }
  });

  test('DELETE - Doctor cannot delete patients (read-only access)', async ({ page }) => {
    // Navigate to my patients
    await page.goto('/trq/doctors/my-patients');
    await page.waitForLoadState('networkidle');

    // Verify delete functionality is not available
    await expect(page.locator('[data-testid="delete-selected-button"]')).not.toBeVisible();
    
    // Check that delete checkbox is not present in patient rows
    await expect(page.locator('[data-testid="select-checkbox"]')).not.toBeVisible();
  });
});