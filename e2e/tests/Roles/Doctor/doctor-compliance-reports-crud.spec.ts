import { test, expect } from '@playwright/test';

test.describe('Doctor - Compliance Reports CRUD', () => {
  const testReport = {
    patientEmail: '<EMAIL>',
    medicalEvaluation: 'Comprehensive medical evaluation completed',
    respiratorClearance: 'fully_cleared',
    clearanceDescription: 'Patient cleared for all respirator types and work environments',
    workloadLimitation: '0',
    followUpRequired: false,
    medicalFindings: [
      {
        category: 'Cardiovascular',
        description: 'Normal heart rate and blood pressure',
        severity: 'low'
      }
    ]
  };

  test.beforeEach(async ({ page }) => {
    // Login as Doctor
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/doctors/home');
  });

  test('CREATE - Doctor can create new compliance report', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Click Create Report button
    await page.click('[data-testid="create-report-button"]');
    await page.waitForSelector('[data-testid="compliance-report-form"]');

    // Select patient
    await page.click('[data-testid="patient-select"]');
    await page.fill('[data-testid="patient-search"]', testReport.patientEmail);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-option-${testReport.patientEmail}"]`);

    // Fill medical evaluation
    await page.fill('[data-testid="medical-evaluation-input"]', testReport.medicalEvaluation);

    // Select respirator clearance
    await page.click('[data-testid="respirator-clearance-select"]');
    await page.click(`[data-value="${testReport.respiratorClearance}"]`);

    // Fill clearance description
    await page.fill('[data-testid="clearance-description-input"]', testReport.clearanceDescription);

    // Set workload limitation
    await page.fill('[data-testid="workload-limitation-input"]', testReport.workloadLimitation);

    // Set follow-up requirement
    if (testReport.followUpRequired) {
      await page.click('[data-testid="follow-up-required-checkbox"]');
    }

    // Add medical findings
    await page.click('[data-testid="add-medical-finding-button"]');
    await page.fill('[data-testid="finding-category-0"]', testReport.medicalFindings[0].category);
    await page.fill('[data-testid="finding-description-0"]', testReport.medicalFindings[0].description);
    await page.click('[data-testid="finding-severity-0"]');
    await page.click(`[data-value="${testReport.medicalFindings[0].severity}"]`);

    // Submit form
    await page.click('[data-testid="submit-report-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Compliance report created successfully');

    // Verify report appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testReport.patientEmail}`)).toBeVisible();
  });

  test('READ - Doctor can view compliance report details', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Search for test report
    await page.fill('[data-testid="search-input"]', testReport.patientEmail);
    await page.waitForTimeout(1000);

    // Click on report to view details
    await page.click(`[data-testid="report-row-${testReport.patientEmail}"] [data-testid="view-button"]`);
    
    // Verify report details page
    await page.waitForURL('**/compliance-reports/*/details');
    await expect(page.locator('[data-testid="report-medical-evaluation"]')).toContainText(testReport.medicalEvaluation);
    await expect(page.locator('[data-testid="report-clearance"]')).toContainText('Fully Cleared');
    await expect(page.locator('[data-testid="report-clearance-description"]')).toContainText(testReport.clearanceDescription);
  });

  test('UPDATE - Doctor can edit compliance report', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Search for test report
    await page.fill('[data-testid="search-input"]', testReport.patientEmail);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="report-row-${testReport.patientEmail}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/compliance-reports/*/edit');

    // Update medical evaluation
    const updatedEvaluation = 'Updated comprehensive medical evaluation with additional notes';
    await page.fill('[data-testid="medical-evaluation-input"]', updatedEvaluation);

    // Add additional medical finding
    await page.click('[data-testid="add-medical-finding-button"]');
    await page.fill('[data-testid="finding-category-1"]', 'Respiratory');
    await page.fill('[data-testid="finding-description-1"]', 'Clear lung sounds, no abnormalities detected');
    await page.click('[data-testid="finding-severity-1"]');
    await page.click('[data-value="low"]');

    // Save changes
    await page.click('[data-testid="save-report-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Compliance report updated successfully');

    // Verify changes in report details
    await expect(page.locator('[data-testid="report-medical-evaluation"]')).toContainText(updatedEvaluation);
  });

  test('Doctor can review and approve compliance report', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Filter for draft reports
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="draft"]');

    // Click review button on first draft report
    await page.click('[data-testid="report-row-0"] [data-testid="review-button"]');
    
    // Verify review page
    await page.waitForURL('**/compliance-reports/*/review');
    await expect(page.locator('[data-testid="report-review-form"]')).toBeVisible();

    // Add review comments
    await page.fill('[data-testid="review-comments"]', 'Report reviewed and approved. All findings are within normal limits.');

    // Approve the report
    await page.click('[data-testid="approve-report-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Report approved successfully');
  });

  test('Doctor can generate PDF report', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Search for approved report
    await page.fill('[data-testid="search-input"]', testReport.patientEmail);
    await page.waitForTimeout(1000);

    // Click on report to view details
    await page.click(`[data-testid="report-row-${testReport.patientEmail}"] [data-testid="view-button"]`);

    // Click generate PDF button
    await page.click('[data-testid="generate-pdf-button"]');

    // Wait for PDF generation (this would typically trigger a download)
    await page.waitForTimeout(2000);

    // Verify PDF generation message or download
    await expect(page.locator('[data-testid="pdf-generated-message"]')).toBeVisible();
  });

  test('Doctor can view patient medical history in report context', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Click create new report
    await page.click('[data-testid="create-report-button"]');
    
    // Select patient
    await page.click('[data-testid="patient-select"]');
    await page.fill('[data-testid="patient-search"]', testReport.patientEmail);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-option-${testReport.patientEmail}"]`);

    // Verify patient medical history is displayed
    await expect(page.locator('[data-testid="patient-medical-history"]')).toBeVisible();
    await expect(page.locator('[data-testid="previous-reports-summary"]')).toBeVisible();

    // Check previous questionnaire responses link
    await expect(page.locator('[data-testid="view-questionnaire-responses"]')).toBeVisible();
  });

  test('DELETE - Doctor can delete draft compliance reports', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Filter for draft reports only
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="draft"]');

    // Search for test report
    await page.fill('[data-testid="search-input"]', testReport.patientEmail);
    await page.waitForTimeout(1000);

    // Select report for deletion (only if it's in draft status)
    await page.click(`[data-testid="report-row-${testReport.patientEmail}"] [data-testid="select-checkbox"]`);

    // Click delete button
    await page.click('[data-testid="delete-selected-button"]');

    // Confirm deletion in dialog
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Report deleted successfully');

    // Verify report no longer appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testReport.patientEmail}`)).not.toBeVisible();
  });

  test('Doctor cannot delete approved compliance reports', async ({ page }) => {
    // Navigate to compliance reports
    await page.goto('/trq/compliance-reports');
    await page.waitForLoadState('networkidle');

    // Filter for approved reports
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="approved"]');

    // Verify delete functionality is not available for approved reports
    const approvedReportRows = page.locator('[data-testid^="report-row-"]');
    const firstRow = approvedReportRows.first();
    
    if (await firstRow.isVisible()) {
      // Check that delete checkbox is not present for approved reports
      await expect(firstRow.locator('[data-testid="select-checkbox"]')).not.toBeVisible();
    }
  });
});