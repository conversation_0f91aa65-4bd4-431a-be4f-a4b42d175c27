import { test, expect } from '@playwright/test';

test.describe('Patient - Profile Management CRUD', () => {
  const patientProfile = {
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    dateOfBirth: '1985-06-15',
    gender: 'Male',
    address: '123 Patient Street',
    city: 'Patient City',
    state: 'PS',
    postalCode: '12345',
    emergencyContactName: '<PERSON>',
    emergencyContactPhone: '******-0124',
    medicalHistory: 'No significant medical history'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Patient
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/patients/home');
  });

  test('READ - Patient can view their profile information', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Verify profile information is displayed
    await expect(page.locator('[data-testid="profile-name"]')).toContainText(`${patientProfile.firstName} ${patientProfile.lastName}`);
    await expect(page.locator('[data-testid="profile-email"]')).toContainText(patientProfile.email);
    await expect(page.locator('[data-testid="profile-phone"]')).toContainText(patientProfile.phone);
    await expect(page.locator('[data-testid="profile-dob"]')).toContainText(patientProfile.dateOfBirth);
    await expect(page.locator('[data-testid="profile-gender"]')).toContainText(patientProfile.gender);
  });

  test('UPDATE - Patient can edit basic profile information', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Click edit profile button
    await page.click('[data-testid="edit-profile-button"]');
    await page.waitForSelector('[data-testid="edit-profile-form"]');

    // Update phone number
    const updatedPhone = '******-9999';
    await page.fill('[data-testid="phone-input"]', updatedPhone);

    // Update address
    const updatedAddress = '456 New Patient Avenue';
    await page.fill('[data-testid="address-input"]', updatedAddress);

    // Save changes
    await page.click('[data-testid="save-profile-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Profile updated successfully');

    // Verify changes are reflected
    await expect(page.locator('[data-testid="profile-phone"]')).toContainText(updatedPhone);
    await expect(page.locator('[data-testid="profile-address"]')).toContainText(updatedAddress);
  });

  test('UPDATE - Patient can update emergency contact information', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to emergency contacts section
    await page.click('[data-testid="emergency-contacts-tab"]');

    // Click edit emergency contact
    await page.click('[data-testid="edit-emergency-contact-button"]');
    await page.waitForSelector('[data-testid="emergency-contact-form"]');

    // Update emergency contact information
    const updatedContactName = 'Mary Smith';
    const updatedContactPhone = '******-8888';
    
    await page.fill('[data-testid="emergency-contact-name"]', updatedContactName);
    await page.fill('[data-testid="emergency-contact-phone"]', updatedContactPhone);
    await page.fill('[data-testid="emergency-contact-relationship"]', 'Sister');

    // Save changes
    await page.click('[data-testid="save-emergency-contact-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="emergency-contact-name-display"]')).toContainText(updatedContactName);
  });

  test('UPDATE - Patient can update medical history information', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to medical history section
    await page.click('[data-testid="medical-history-tab"]');

    // Click edit medical history
    await page.click('[data-testid="edit-medical-history-button"]');
    await page.waitForSelector('[data-testid="medical-history-form"]');

    // Update medical history
    const updatedMedicalHistory = 'Mild seasonal allergies. No other significant medical history.';
    await page.fill('[data-testid="medical-history-textarea"]', updatedMedicalHistory);

    // Add allergies
    await page.click('[data-testid="add-allergy-button"]');
    await page.fill('[data-testid="allergy-name-0"]', 'Pollen');
    await page.fill('[data-testid="allergy-severity-0"]', 'Mild');

    // Add medications
    await page.click('[data-testid="add-medication-button"]');
    await page.fill('[data-testid="medication-name-0"]', 'Claritin');
    await page.fill('[data-testid="medication-dosage-0"]', '10mg daily');

    // Save changes
    await page.click('[data-testid="save-medical-history-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="medical-history-display"]')).toContainText(updatedMedicalHistory);
  });

  test('READ - Patient can view their healthcare providers', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to healthcare providers section
    await page.click('[data-testid="healthcare-providers-tab"]');

    // Verify healthcare providers information
    await expect(page.locator('[data-testid="assigned-doctor"]')).toBeVisible();
    await expect(page.locator('[data-testid="clinic-information"]')).toBeVisible();
    await expect(page.locator('[data-testid="primary-care-provider"]')).toBeVisible();
  });

  test('Patient can view their compliance status and history', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to compliance section
    await page.click('[data-testid="compliance-tab"]');

    // Verify compliance information
    await expect(page.locator('[data-testid="current-compliance-status"]')).toBeVisible();
    await expect(page.locator('[data-testid="last-evaluation-date"]')).toBeVisible();
    await expect(page.locator('[data-testid="next-evaluation-due"]')).toBeVisible();
    await expect(page.locator('[data-testid="respirator-clearance-status"]')).toBeVisible();

    // Check compliance history
    await expect(page.locator('[data-testid="compliance-history-timeline"]')).toBeVisible();
  });

  test('Patient can download their medical records', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to medical records section
    await page.click('[data-testid="medical-records-tab"]');

    // Download complete medical record
    await page.click('[data-testid="download-complete-record-button"]');

    // Verify download initiated
    await expect(page.locator('[data-testid="download-started-message"]')).toBeVisible();

    // Download specific report
    await page.click('[data-testid="download-latest-report-button"]');
    await expect(page.locator('[data-testid="report-download-started"]')).toBeVisible();
  });

  test('Patient can update notification preferences', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to settings section
    await page.click('[data-testid="settings-tab"]');

    // Update notification preferences
    await page.click('[data-testid="email-notifications-toggle"]');
    await page.click('[data-testid="sms-notifications-toggle"]');
    
    // Set notification frequency
    await page.click('[data-testid="notification-frequency-select"]');
    await page.click('[data-value="weekly"]');

    // Save settings
    await page.click('[data-testid="save-settings-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Notification preferences updated');
  });

  test('Patient can change their password', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to security section
    await page.click('[data-testid="security-tab"]');

    // Click change password
    await page.click('[data-testid="change-password-button"]');
    await page.waitForSelector('[data-testid="change-password-form"]');

    // Fill password change form
    await page.fill('[data-testid="current-password"]', 'password123');
    await page.fill('[data-testid="new-password"]', 'newpassword123');
    await page.fill('[data-testid="confirm-new-password"]', 'newpassword123');

    // Submit password change
    await page.click('[data-testid="update-password-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Password updated successfully');
  });

  test('Patient can view their appointment history', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to appointments section
    await page.click('[data-testid="appointments-tab"]');

    // Verify appointments information
    await expect(page.locator('[data-testid="upcoming-appointments"]')).toBeVisible();
    await expect(page.locator('[data-testid="past-appointments"]')).toBeVisible();
    
    // Check appointment details
    const firstAppointment = page.locator('[data-testid="appointment-row-0"]');
    if (await firstAppointment.isVisible()) {
      await expect(firstAppointment.locator('[data-testid="appointment-date"]')).toBeVisible();
      await expect(firstAppointment.locator('[data-testid="appointment-provider"]')).toBeVisible();
      await expect(firstAppointment.locator('[data-testid="appointment-type"]')).toBeVisible();
    }
  });

  test('Patient cannot edit critical medical information', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Verify certain fields are read-only
    await expect(page.locator('[data-testid="patient-id"]')).toBeVisible();
    await expect(page.locator('[data-testid="patient-id"]')).toHaveAttribute('readonly');
    
    // Date of birth should be read-only
    await expect(page.locator('[data-testid="profile-dob"]')).toBeVisible();
    
    // Medical record number should be read-only
    await expect(page.locator('[data-testid="medical-record-number"]')).toBeVisible();
  });

  test('Patient cannot delete their own profile', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to security section
    await page.click('[data-testid="security-tab"]');

    // Verify delete account option is not available
    await expect(page.locator('[data-testid="delete-account-button"]')).not.toBeVisible();
    
    // Or if it exists, it should require admin approval
    const deleteButton = page.locator('[data-testid="request-account-deletion-button"]');
    if (await deleteButton.isVisible()) {
      await deleteButton.click();
      await expect(page.locator('[data-testid="deletion-request-info"]')).toContainText('requires approval');
    }
  });

  test('Patient can view data privacy and consent information', async ({ page }) => {
    // Navigate to profile page
    await page.goto('/trq/patients/profile');
    await page.waitForLoadState('networkidle');

    // Navigate to privacy section
    await page.click('[data-testid="privacy-tab"]');

    // Verify privacy information is displayed
    await expect(page.locator('[data-testid="data-sharing-preferences"]')).toBeVisible();
    await expect(page.locator('[data-testid="consent-history"]')).toBeVisible();
    await expect(page.locator('[data-testid="privacy-policy-link"]')).toBeVisible();
    
    // Check data export option
    await expect(page.locator('[data-testid="export-my-data-button"]')).toBeVisible();
  });
});