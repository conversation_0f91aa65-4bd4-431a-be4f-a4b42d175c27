import { test, expect } from '@playwright/test';

test.describe('Patient - Questionnaire Management CRUD', () => {
  const testQuestionnaire = {
    title: 'Respiratory Health Assessment',
    category: 'Health Screening',
    estimatedTime: '10-15 minutes'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Patient
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/patients/home');
  });

  test('READ - Patient can view assigned questionnaires', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Verify questionnaires list is displayed
    await expect(page.locator('[data-testid="questionnaires-list"]')).toBeVisible();
    
    // Check for different questionnaire statuses
    await expect(page.locator('[data-testid="pending-questionnaires"]')).toBeVisible();
    await expect(page.locator('[data-testid="in-progress-questionnaires"]')).toBeVisible();
    await expect(page.locator('[data-testid="completed-questionnaires"]')).toBeVisible();
  });

  test('READ - Patient can view questionnaire details', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Click on first questionnaire to view details
    await page.click('[data-testid="questionnaire-row-0"] [data-testid="view-button"]');
    
    // Verify questionnaire details page
    await page.waitForURL('**/questionnaires/*/details');
    await expect(page.locator('[data-testid="questionnaire-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="questionnaire-description"]')).toBeVisible();
    await expect(page.locator('[data-testid="questionnaire-category"]')).toBeVisible();
    await expect(page.locator('[data-testid="estimated-time"]')).toBeVisible();
    await expect(page.locator('[data-testid="due-date"]')).toBeVisible();
  });

  test('CREATE - Patient can start new questionnaire', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Find a pending questionnaire and start it
    await page.click('[data-testid="questionnaire-row-pending"] [data-testid="start-button"]');
    
    // Verify questionnaire wizard opens
    await page.waitForURL('**/questionnaires/wizard/*');
    await expect(page.locator('[data-testid="questionnaire-wizard"]')).toBeVisible();
    await expect(page.locator('[data-testid="question-1"]')).toBeVisible();
    await expect(page.locator('[data-testid="progress-indicator"]')).toBeVisible();
  });

  test('UPDATE - Patient can continue in-progress questionnaire', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Find an in-progress questionnaire and continue it
    await page.click('[data-testid="questionnaire-row-in-progress"] [data-testid="continue-button"]');
    
    // Verify questionnaire wizard opens at saved progress
    await page.waitForURL('**/questionnaires/wizard/*');
    await expect(page.locator('[data-testid="questionnaire-wizard"]')).toBeVisible();
    
    // Check that progress is maintained
    await expect(page.locator('[data-testid="progress-indicator"]')).toContainText('%'); // Some progress shown
  });

  test('UPDATE - Patient can answer questionnaire questions', async ({ page }) => {
    // Navigate to questionnaire wizard
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');
    await page.click('[data-testid="questionnaire-row-0"] [data-testid="start-button"]');
    
    // Answer first question (multiple choice)
    await page.click('[data-testid="question-1-option-1"]');
    
    // Move to next question
    await page.click('[data-testid="next-question-button"]');
    
    // Answer text question
    await page.fill('[data-testid="question-2-text-input"]', 'No, I do not experience any breathing difficulties during normal activities.');
    
    // Move to next question
    await page.click('[data-testid="next-question-button"]');
    
    // Answer rating scale question
    await page.click('[data-testid="question-3-rating-7"]');
    
    // Save progress
    await page.click('[data-testid="save-progress-button"]');
    
    // Verify progress saved
    await expect(page.locator('[data-testid="progress-saved-message"]')).toBeVisible();
  });

  test('UPDATE - Patient can complete questionnaire', async ({ page }) => {
    // Navigate to questionnaire wizard
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');
    await page.click('[data-testid="questionnaire-row-0"] [data-testid="continue-button"]');
    
    // Complete remaining questions quickly
    await page.click('[data-testid="question-4-option-2"]');
    await page.click('[data-testid="next-question-button"]');
    
    await page.fill('[data-testid="question-5-text-input"]', 'I work in a clean office environment.');
    await page.click('[data-testid="next-question-button"]');
    
    // Reach final question
    await page.click('[data-testid="question-6-option-1"]');
    
    // Submit questionnaire
    await page.click('[data-testid="submit-questionnaire-button"]');
    
    // Verify completion confirmation
    await expect(page.locator('[data-testid="completion-confirmation"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaire completed successfully');
    
    // Verify redirect to completion page
    await page.waitForURL('**/questionnaires/*/completed');
  });

  test('READ - Patient can review completed questionnaire responses', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Filter for completed questionnaires
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="completed"]');

    // Click on completed questionnaire to review
    await page.click('[data-testid="questionnaire-row-completed"] [data-testid="review-button"]');
    
    // Verify review page
    await page.waitForURL('**/questionnaires/*/review');
    await expect(page.locator('[data-testid="questionnaire-responses-summary"]')).toBeVisible();
    await expect(page.locator('[data-testid="completion-date"]')).toBeVisible();
    await expect(page.locator('[data-testid="responses-list"]')).toBeVisible();
  });

  test('Patient can download questionnaire completion certificate', async ({ page }) => {
    // Navigate to completed questionnaire
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');
    
    // Filter for completed questionnaires
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="completed"]');
    
    // Click on completed questionnaire
    await page.click('[data-testid="questionnaire-row-completed"] [data-testid="view-button"]');
    
    // Download completion certificate
    await page.click('[data-testid="download-certificate-button"]');
    
    // Verify download
    await expect(page.locator('[data-testid="download-started-message"]')).toBeVisible();
  });

  test('Patient can view questionnaire history and timeline', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Check questionnaire history section
    await expect(page.locator('[data-testid="questionnaire-timeline"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-completed-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="average-completion-time-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="completion-rate-stat"]')).toBeVisible();
  });

  test('Patient cannot edit completed questionnaires', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Filter for completed questionnaires
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="completed"]');

    // Verify edit functionality is not available
    await expect(page.locator('[data-testid="questionnaire-row-completed"] [data-testid="edit-button"]')).not.toBeVisible();
    
    // Click on completed questionnaire
    await page.click('[data-testid="questionnaire-row-completed"] [data-testid="view-button"]');
    
    // Verify no edit options are available
    await expect(page.locator('[data-testid="edit-responses-button"]')).not.toBeVisible();
  });

  test('Patient cannot delete questionnaires', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Verify delete functionality is not available
    await expect(page.locator('[data-testid="delete-button"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="select-checkbox"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="bulk-delete-button"]')).not.toBeVisible();
  });

  test('Patient can request questionnaire extension if overdue', async ({ page }) => {
    // Navigate to my questionnaires
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');

    // Look for overdue questionnaires
    const overdueQuestionnaire = page.locator('[data-testid="questionnaire-status-overdue"]').first();
    
    if (await overdueQuestionnaire.isVisible()) {
      // Click request extension
      await page.click('[data-testid="request-extension-button"]');
      await page.waitForSelector('[data-testid="extension-request-dialog"]');
      
      // Fill extension request
      await page.fill('[data-testid="extension-reason"]', 'I need additional time due to work schedule conflicts');
      await page.fill('[data-testid="requested-extension-days"]', '3');
      
      // Submit request
      await page.click('[data-testid="submit-extension-request"]');
      
      // Verify success
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Extension request submitted');
    }
  });

  test('Patient can view questionnaire feedback from healthcare provider', async ({ page }) => {
    // Navigate to completed questionnaire with feedback
    await page.goto('/trq/questionnaires/my-questionnaires');
    await page.waitForLoadState('networkidle');
    
    // Filter for reviewed questionnaires
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="reviewed"]');
    
    // Click on reviewed questionnaire
    await page.click('[data-testid="questionnaire-row-reviewed"] [data-testid="view-button"]');
    
    // Check for healthcare provider feedback
    await expect(page.locator('[data-testid="provider-feedback-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="feedback-comments"]')).toBeVisible();
    await expect(page.locator('[data-testid="reviewer-name"]')).toBeVisible();
    await expect(page.locator('[data-testid="review-date"]')).toBeVisible();
  });
});