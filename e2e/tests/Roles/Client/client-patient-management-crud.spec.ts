import { test, expect } from '@playwright/test';

test.describe('Client - Patient Management CRUD', () => {
  const testPatient = {
    firstName: 'Client',
    lastName: 'Employee',
    email: '<EMAIL>',
    dateOfBirth: '1988-03-20',
    gender: 'Other',
    phone: '******-2222',
    employeeId: 'EMP001',
    department: 'Manufacturing',
    position: 'Machine Operator'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Client
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/clients/home');
  });

  test('CREATE - Client can add new employee/patient', async ({ page }) => {
    // Navigate to my patients (employees)
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');

    // Click Add Employee button
    await page.click('[data-testid="add-patient-button"]');
    await page.waitForSelector('[data-testid="add-patient-dialog"]');

    // Fill employee form
    await page.fill('[data-testid="first-name-input"]', testPatient.firstName);
    await page.fill('[data-testid="last-name-input"]', testPatient.lastName);
    await page.fill('[data-testid="email-input"]', testPatient.email);
    await page.fill('[data-testid="date-of-birth-input"]', testPatient.dateOfBirth);
    
    // Select gender
    await page.click('[data-testid="gender-select"]');
    await page.click(`[data-value="${testPatient.gender}"]`);

    await page.fill('[data-testid="phone-input"]', testPatient.phone);
    await page.fill('[data-testid="employee-id-input"]', testPatient.employeeId);
    await page.fill('[data-testid="department-input"]', testPatient.department);
    await page.fill('[data-testid="position-input"]', testPatient.position);

    // Submit form
    await page.click('[data-testid="submit-patient-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Employee added successfully');

    // Verify employee appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testPatient.email}`)).toBeVisible();
  });

  test('READ - Client can view employee details', async ({ page }) => {
    // Navigate to my patients (employees)
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');

    // Search for test employee
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Click on employee to view details
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);
    
    // Verify employee details page
    await page.waitForURL('**/patients/*/details');
    await expect(page.locator('[data-testid="patient-details-name"]')).toContainText(`${testPatient.firstName} ${testPatient.lastName}`);
    await expect(page.locator('[data-testid="patient-details-email"]')).toContainText(testPatient.email);
    await expect(page.locator('[data-testid="patient-details-employee-id"]')).toContainText(testPatient.employeeId);
    await expect(page.locator('[data-testid="patient-details-department"]')).toContainText(testPatient.department);
  });

  test('UPDATE - Client can update employee information', async ({ page }) => {
    // Navigate to my patients (employees)
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');

    // Search for test employee
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/patients/*/edit');

    // Update employee information
    const updatedDepartment = 'Safety Department';
    const updatedPosition = 'Safety Coordinator';
    await page.fill('[data-testid="department-input"]', updatedDepartment);
    await page.fill('[data-testid="position-input"]', updatedPosition);

    // Save changes
    await page.click('[data-testid="save-patient-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Employee updated successfully');

    // Verify changes in employee details
    await expect(page.locator('[data-testid="patient-details-department"]')).toContainText(updatedDepartment);
    await expect(page.locator('[data-testid="patient-details-position"]')).toContainText(updatedPosition);
  });

  test('Client can request questionnaire assignment for employee', async ({ page }) => {
    // Navigate to employee details
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on employee
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to questionnaires tab
    await page.click('[data-testid="questionnaires-tab"]');

    // Request questionnaire assignment
    await page.click('[data-testid="request-questionnaire-button"]');
    await page.waitForSelector('[data-testid="questionnaire-request-dialog"]');
    
    // Select questionnaire type
    await page.click('[data-testid="questionnaire-type-select"]');
    await page.click('[data-value="respiratory-health"]');

    // Add justification
    await page.fill('[data-testid="request-justification"]', 'Employee will be working with respirators in the new facility');

    // Set requested completion date
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);
    await page.fill('[data-testid="requested-completion-date"]', futureDate.toISOString().split('T')[0]);

    // Submit request
    await page.click('[data-testid="submit-request-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaire request submitted');
  });

  test('Client can view employee compliance status', async ({ page }) => {
    // Navigate to employee details
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on employee
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to compliance tab
    await page.click('[data-testid="compliance-tab"]');

    // Verify compliance information is displayed
    await expect(page.locator('[data-testid="compliance-status"]')).toBeVisible();
    await expect(page.locator('[data-testid="last-evaluation-date"]')).toBeVisible();
    await expect(page.locator('[data-testid="next-evaluation-due"]')).toBeVisible();
    await expect(page.locator('[data-testid="respirator-clearance-status"]')).toBeVisible();
  });

  test('Client can download employee compliance reports', async ({ page }) => {
    // Navigate to employee details
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on employee
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to compliance tab
    await page.click('[data-testid="compliance-tab"]');

    // Download latest compliance report
    await page.click('[data-testid="download-latest-report-button"]');

    // Verify download initiated
    await expect(page.locator('[data-testid="download-initiated-message"]')).toBeVisible();
  });

  test('Client can bulk assign questionnaires to multiple employees', async ({ page }) => {
    // Navigate to my patients (employees)
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');

    // Select multiple employees
    await page.click('[data-testid="select-all-checkbox"]');

    // Click bulk assign questionnaire
    await page.click('[data-testid="bulk-assign-questionnaire-button"]');
    await page.waitForSelector('[data-testid="bulk-assign-dialog"]');

    // Select questionnaire type
    await page.click('[data-testid="questionnaire-type-select"]');
    await page.click('[data-value="annual-health-screening"]');

    // Set due date
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 14);
    await page.fill('[data-testid="due-date-input"]', futureDate.toISOString().split('T')[0]);

    // Submit bulk assignment
    await page.click('[data-testid="submit-bulk-assignment-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaires assigned to all selected employees');
  });

  test('DELETE - Client can remove employee (limited access)', async ({ page }) => {
    // Navigate to my patients (employees)
    await page.goto('/trq/clients/my-patients');
    await page.waitForLoadState('networkidle');

    // Search for test employee
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Note: Clients typically have limited delete permissions
    // They might only be able to "deactivate" rather than delete
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="edit-button"]`);
    
    // Look for deactivate option instead of delete
    await expect(page.locator('[data-testid="deactivate-employee-button"]')).toBeVisible();
    
    // Click deactivate
    await page.click('[data-testid="deactivate-employee-button"]');
    await page.waitForSelector('[data-testid="confirm-deactivate-dialog"]');
    
    // Confirm deactivation
    await page.click('[data-testid="confirm-deactivate-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Employee deactivated successfully');
  });

  test('Client can view department-wide compliance dashboard', async ({ page }) => {
    // Navigate to client dashboard
    await page.goto('/trq/clients/home');
    await page.waitForLoadState('networkidle');

    // Verify dashboard elements
    await expect(page.locator('[data-testid="total-employees-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="compliant-employees-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="pending-evaluations-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="overdue-evaluations-stat"]')).toBeVisible();

    // Check compliance chart
    await expect(page.locator('[data-testid="compliance-chart"]')).toBeVisible();
    
    // Check department breakdown
    await expect(page.locator('[data-testid="department-breakdown-table"]')).toBeVisible();
  });
});