import { test, expect } from '@playwright/test';

test.describe('Client - Purchase Management CRUD', () => {
  const testPurchase = {
    productName: 'Respiratory Health Assessment Package',
    quantity: 5,
    unitPrice: 150.00,
    totalPrice: 750.00
  };

  test.beforeEach(async ({ page }) => {
    // Login as Client
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/clients/home');
  });

  test('CREATE - Client can create new purchase order', async ({ page }) => {
    // Navigate to products page to start purchase
    await page.goto('/trq/products');
    await page.waitForLoadState('networkidle');

    // Search for product
    await page.fill('[data-testid="product-search"]', testPurchase.productName);
    await page.waitForTimeout(1000);

    // Add product to cart
    await page.click(`[data-testid="product-${testPurchase.productName}"] [data-testid="add-to-cart-button"]`);
    
    // Set quantity
    await page.fill('[data-testid="quantity-input"]', testPurchase.quantity.toString());
    
    // Confirm add to cart
    await page.click('[data-testid="confirm-add-to-cart"]');

    // Navigate to cart
    await page.goto('/trq/purchases/cart');
    await page.waitForLoadState('networkidle');

    // Verify cart contents
    await expect(page.locator(`text=${testPurchase.productName}`)).toBeVisible();
    await expect(page.locator('[data-testid="cart-total"]')).toContainText(`$${testPurchase.totalPrice}`);

    // Proceed to checkout
    await page.click('[data-testid="proceed-to-checkout-button"]');
    await page.waitForURL('**/purchases/cart/checkout');

    // Fill billing information
    await page.fill('[data-testid="billing-contact-name"]', 'John Smith');
    await page.fill('[data-testid="billing-email"]', '<EMAIL>');
    await page.fill('[data-testid="billing-address"]', '123 Business Ave');
    await page.fill('[data-testid="billing-city"]', 'Business City');
    await page.fill('[data-testid="billing-state"]', 'BC');
    await page.fill('[data-testid="billing-postal-code"]', '12345');

    // Submit order
    await page.click('[data-testid="submit-order-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Purchase order created successfully');

    // Verify redirect to order confirmation
    await page.waitForURL('**/purchases/*/details');
    await expect(page.locator('[data-testid="order-confirmation"]')).toBeVisible();
  });

  test('READ - Client can view purchase history and details', async ({ page }) => {
    // Navigate to my purchases
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');

    // Search for test purchase
    await page.fill('[data-testid="search-input"]', testPurchase.productName);
    await page.waitForTimeout(1000);

    // Click on purchase to view details
    await page.click(`[data-testid="purchase-row-0"] [data-testid="view-button"]`);
    
    // Verify purchase details page
    await page.waitForURL('**/purchases/*/details');
    await expect(page.locator('[data-testid="purchase-details-product"]')).toContainText(testPurchase.productName);
    await expect(page.locator('[data-testid="purchase-details-quantity"]')).toContainText(testPurchase.quantity.toString());
    await expect(page.locator('[data-testid="purchase-details-total"]')).toContainText(`$${testPurchase.totalPrice}`);
  });

  test('Client can view purchase analytics and reporting', async ({ page }) => {
    // Navigate to my purchases
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');

    // Check analytics section
    await expect(page.locator('[data-testid="total-purchases-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-amount-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="pending-purchases-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="completed-purchases-stat"]')).toBeVisible();

    // Check purchase breakdown by product type
    await expect(page.locator('[data-testid="product-breakdown-chart"]')).toBeVisible();
    
    // Check monthly spending chart
    await expect(page.locator('[data-testid="monthly-spending-chart"]')).toBeVisible();
  });

  test('Client can download purchase invoices', async ({ page }) => {
    // Navigate to purchase details
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');
    
    // Find and click on completed purchase
    await page.click('[data-testid="purchase-row-0"] [data-testid="view-button"]');

    // Download invoice
    await page.click('[data-testid="download-invoice-button"]');

    // Verify download initiated
    await expect(page.locator('[data-testid="download-started-message"]')).toBeVisible();
  });

  test('Client can track purchase status and delivery', async ({ page }) => {
    // Navigate to purchase details
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');
    
    // Find and click on purchase
    await page.click('[data-testid="purchase-row-0"] [data-testid="view-button"]');

    // Check tracking information
    await expect(page.locator('[data-testid="purchase-status"]')).toBeVisible();
    await expect(page.locator('[data-testid="order-date"]')).toBeVisible();
    await expect(page.locator('[data-testid="expected-delivery"]')).toBeVisible();
    
    // Check status timeline
    await expect(page.locator('[data-testid="status-timeline"]')).toBeVisible();
  });

  test('Client can reorder previous purchases', async ({ page }) => {
    // Navigate to purchase details
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');
    
    // Find and click on purchase
    await page.click('[data-testid="purchase-row-0"] [data-testid="view-button"]');

    // Click reorder button
    await page.click('[data-testid="reorder-button"]');
    
    // Verify items added to cart
    await page.waitForURL('**/purchases/cart');
    await expect(page.locator(`text=${testPurchase.productName}`)).toBeVisible();
    await expect(page.locator('[data-testid="cart-notification"]')).toContainText('Items added to cart from previous order');
  });

  test('Client can manage recurring orders', async ({ page }) => {
    // Navigate to purchase details
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');
    
    // Find and click on purchase
    await page.click('[data-testid="purchase-row-0"] [data-testid="view-button"]');

    // Set up recurring order
    await page.click('[data-testid="setup-recurring-order-button"]');
    await page.waitForSelector('[data-testid="recurring-order-dialog"]');

    // Configure recurrence
    await page.click('[data-testid="frequency-select"]');
    await page.click('[data-value="monthly"]');

    // Set start date
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    await page.fill('[data-testid="start-date-input"]', nextMonth.toISOString().split('T')[0]);

    // Submit recurring order setup
    await page.click('[data-testid="setup-recurring-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Recurring order set up successfully');
  });

  test('Client can request bulk pricing quotes', async ({ page }) => {
    // Navigate to products
    await page.goto('/trq/products');
    await page.waitForLoadState('networkidle');

    // Select multiple products
    await page.click('[data-testid="product-0"] [data-testid="select-checkbox"]');
    await page.click('[data-testid="product-1"] [data-testid="select-checkbox"]');

    // Request bulk quote
    await page.click('[data-testid="request-bulk-quote-button"]');
    await page.waitForSelector('[data-testid="bulk-quote-dialog"]');

    // Fill quote request details
    await page.fill('[data-testid="estimated-quantity"]', '50');
    await page.fill('[data-testid="quote-message"]', 'Looking for bulk pricing for annual employee health assessments');

    // Submit quote request
    await page.click('[data-testid="submit-quote-request-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Quote request submitted successfully');
  });

  test('UPDATE - Client can modify pending orders', async ({ page }) => {
    // Navigate to my purchases
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');

    // Filter for pending orders
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="pending"]');

    // Find pending order and click modify
    await page.click('[data-testid="purchase-row-0"] [data-testid="modify-button"]');
    
    // Update quantity
    await page.fill('[data-testid="quantity-input-0"]', '10');

    // Save changes
    await page.click('[data-testid="save-changes-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Order updated successfully');
  });

  test('DELETE - Client can cancel pending orders', async ({ page }) => {
    // Navigate to my purchases
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');

    // Filter for pending orders
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="pending"]');

    // Find pending order and click cancel
    await page.click('[data-testid="purchase-row-0"] [data-testid="cancel-button"]');
    
    // Confirm cancellation
    await page.waitForSelector('[data-testid="confirm-cancel-dialog"]');
    await page.fill('[data-testid="cancellation-reason"]', 'Changed business requirements');
    await page.click('[data-testid="confirm-cancel-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Order cancelled successfully');

    // Verify order status changed to cancelled
    await expect(page.locator('[data-testid="purchase-status"]')).toContainText('Cancelled');
  });

  test('Client cannot delete completed/shipped orders', async ({ page }) => {
    // Navigate to my purchases
    await page.goto('/trq/purchases/my-purchases');
    await page.waitForLoadState('networkidle');

    // Filter for completed orders
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="completed"]');

    // Verify cancel/delete buttons are not available for completed orders
    const completedOrderRows = page.locator('[data-testid^="purchase-row-"]');
    const firstRow = completedOrderRows.first();
    
    if (await firstRow.isVisible()) {
      await expect(firstRow.locator('[data-testid="cancel-button"]')).not.toBeVisible();
      await expect(firstRow.locator('[data-testid="delete-button"]')).not.toBeVisible();
    }
  });
});