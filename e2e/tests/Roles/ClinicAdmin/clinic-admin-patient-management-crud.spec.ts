import { test, expect } from '@playwright/test';

test.describe('ClinicAdmin - Patient Management CRUD', () => {
  const testPatient = {
    firstName: 'Test',
    lastName: 'Patient',
    email: '<EMAIL>',
    dateOfBirth: '1990-01-01',
    gender: 'Male',
    phone: '******-7890',
    address: '789 Patient Lane'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Clinic Admin
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/clinic-admins/home');
  });

  test('CREATE - ClinicAdmin can create new patient', async ({ page }) => {
    // Navigate to patients list
    await page.goto('/trq/patients');
    await page.waitForLoadState('networkidle');

    // Click Add Patient button
    await page.click('[data-testid="add-patient-button"]');
    await page.waitForSelector('[data-testid="add-patient-dialog"]');

    // Fill patient form
    await page.fill('[data-testid="first-name-input"]', testPatient.firstName);
    await page.fill('[data-testid="last-name-input"]', testPatient.lastName);
    await page.fill('[data-testid="email-input"]', testPatient.email);
    await page.fill('[data-testid="date-of-birth-input"]', testPatient.dateOfBirth);
    
    // Select gender
    await page.click('[data-testid="gender-select"]');
    await page.click(`[data-value="${testPatient.gender}"]`);

    await page.fill('[data-testid="phone-input"]', testPatient.phone);
    await page.fill('[data-testid="address-input"]', testPatient.address);

    // Submit form
    await page.click('[data-testid="submit-patient-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Patient added successfully');

    // Verify patient appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testPatient.email}`)).toBeVisible();
  });

  test('READ - ClinicAdmin can view patient details', async ({ page }) => {
    // Navigate to patients list
    await page.goto('/trq/patients');
    await page.waitForLoadState('networkidle');

    // Search for test patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Click on patient to view details
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);
    
    // Verify patient details page
    await page.waitForURL('**/patients/*/details');
    await expect(page.locator('[data-testid="patient-details-name"]')).toContainText(`${testPatient.firstName} ${testPatient.lastName}`);
    await expect(page.locator('[data-testid="patient-details-email"]')).toContainText(testPatient.email);
    await expect(page.locator('[data-testid="patient-details-gender"]')).toContainText(testPatient.gender);
  });

  test('UPDATE - ClinicAdmin can edit patient information', async ({ page }) => {
    // Navigate to patients list
    await page.goto('/trq/patients');
    await page.waitForLoadState('networkidle');

    // Search for test patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/patients/*/edit');

    // Update patient information
    const updatedPhone = '******-9999';
    await page.fill('[data-testid="phone-input"]', updatedPhone);

    // Save changes
    await page.click('[data-testid="save-patient-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Patient updated successfully');

    // Verify changes in patient details
    await expect(page.locator('[data-testid="patient-details-phone"]')).toContainText(updatedPhone);
  });

  test('DELETE - ClinicAdmin can delete patient', async ({ page }) => {
    // Navigate to patients list
    await page.goto('/trq/patients');
    await page.waitForLoadState('networkidle');

    // Search for test patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);

    // Select patient for deletion
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="select-checkbox"]`);

    // Click delete button
    await page.click('[data-testid="delete-selected-button"]');

    // Confirm deletion in dialog
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Patient deleted successfully');

    // Verify patient no longer appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testPatient.email}`)).not.toBeVisible();
  });

  test('ClinicAdmin can assign doctor to patient', async ({ page }) => {
    // Navigate to patient details
    await page.goto('/trq/patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Assign doctor
    await page.click('[data-testid="assign-doctor-button"]');
    await page.waitForSelector('[data-testid="doctor-select"]');
    
    // Select a doctor
    await page.click('[data-testid="doctor-select"]');
    await page.click('[data-value="doctor-1"]'); // This would be dynamic in real tests

    // Confirm assignment
    await page.click('[data-testid="confirm-assignment-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="assigned-doctor"]')).toBeVisible();
  });

  test('ClinicAdmin can view patient questionnaires', async ({ page }) => {
    // Navigate to patient details
    await page.goto('/trq/patients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on patient
    await page.fill('[data-testid="search-input"]', testPatient.email);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="patient-row-${testPatient.email}"] [data-testid="view-button"]`);

    // Navigate to questionnaires tab
    await page.click('[data-testid="questionnaires-tab"]');

    // Verify questionnaires section is accessible
    await expect(page.locator('[data-testid="patient-questionnaires-list"]')).toBeVisible();
    
    // Check if assign questionnaire button is available
    await expect(page.locator('[data-testid="assign-questionnaire-button"]')).toBeVisible();
  });
});