import { test, expect } from '@playwright/test';

test.describe('ClinicAdmin - Questionnaire Management CRUD', () => {
  const testQuestionnaire = {
    title: 'Test Clinic Questionnaire',
    description: 'A test questionnaire created by clinic admin',
    category: 'Respiratory Health',
    estimatedTime: '15 minutes'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Clinic Admin
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/clinic-admins/home');
  });

  test('CREATE - ClinicAdmin can create new questionnaire', async ({ page }) => {
    // Navigate to questionnaires list
    await page.goto('/trq/questionnaires');
    await page.waitForLoadState('networkidle');

    // Click Add Questionnaire button
    await page.click('[data-testid="add-questionnaire-button"]');
    await page.waitForSelector('[data-testid="create-questionnaire-form"]');

    // Fill questionnaire form
    await page.fill('[data-testid="title-input"]', testQuestionnaire.title);
    await page.fill('[data-testid="description-input"]', testQuestionnaire.description);
    
    // Select category
    await page.click('[data-testid="category-select"]');
    await page.click(`[data-value="${testQuestionnaire.category}"]`);

    await page.fill('[data-testid="estimated-time-input"]', testQuestionnaire.estimatedTime);

    // Add sample questions
    await page.click('[data-testid="add-question-button"]');
    await page.fill('[data-testid="question-text-0"]', 'Do you have any respiratory symptoms?');
    await page.click('[data-testid="question-type-0"]');
    await page.click('[data-value="multiple-choice"]');

    // Add options for multiple choice
    await page.click('[data-testid="add-option-button-0"]');
    await page.fill('[data-testid="option-text-0-0"]', 'Yes');
    await page.click('[data-testid="add-option-button-0"]');
    await page.fill('[data-testid="option-text-0-1"]', 'No');

    // Submit form
    await page.click('[data-testid="submit-questionnaire-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaire created successfully');

    // Verify questionnaire appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testQuestionnaire.title}`)).toBeVisible();
  });

  test('READ - ClinicAdmin can view questionnaire details', async ({ page }) => {
    // Navigate to questionnaires list
    await page.goto('/trq/questionnaires');
    await page.waitForLoadState('networkidle');

    // Search for test questionnaire
    await page.fill('[data-testid="search-input"]', testQuestionnaire.title);
    await page.waitForTimeout(1000);

    // Click on questionnaire to view details
    await page.click(`[data-testid="questionnaire-row-${testQuestionnaire.title}"] [data-testid="view-button"]`);
    
    // Verify questionnaire details page
    await page.waitForURL('**/questionnaires/*/details');
    await expect(page.locator('[data-testid="questionnaire-details-title"]')).toContainText(testQuestionnaire.title);
    await expect(page.locator('[data-testid="questionnaire-details-description"]')).toContainText(testQuestionnaire.description);
    await expect(page.locator('[data-testid="questionnaire-details-category"]')).toContainText(testQuestionnaire.category);
  });

  test('UPDATE - ClinicAdmin can edit questionnaire', async ({ page }) => {
    // Navigate to questionnaires list
    await page.goto('/trq/questionnaires');
    await page.waitForLoadState('networkidle');

    // Search for test questionnaire
    await page.fill('[data-testid="search-input"]', testQuestionnaire.title);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="questionnaire-row-${testQuestionnaire.title}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/questionnaires/*/edit');

    // Update questionnaire information
    const updatedTitle = 'Updated Test Clinic Questionnaire';
    await page.fill('[data-testid="title-input"]', updatedTitle);

    // Save changes
    await page.click('[data-testid="save-questionnaire-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaire updated successfully');

    // Verify changes in questionnaire details
    await expect(page.locator('[data-testid="questionnaire-details-title"]')).toContainText(updatedTitle);
  });

  test('ClinicAdmin can assign questionnaire to patient', async ({ page }) => {
    // Navigate to questionnaires list
    await page.goto('/trq/questionnaires');
    await page.waitForLoadState('networkidle');

    // Search for test questionnaire
    await page.fill('[data-testid="search-input"]', testQuestionnaire.title);
    await page.waitForTimeout(1000);

    // Click assign button
    await page.click(`[data-testid="questionnaire-row-${testQuestionnaire.title}"] [data-testid="assign-button"]`);
    
    // Wait for assignment dialog
    await page.waitForSelector('[data-testid="assign-questionnaire-dialog"]');

    // Select patients to assign to
    await page.click('[data-testid="patient-search"]');
    await page.fill('[data-testid="patient-search-input"]', 'test patient');
    await page.waitForTimeout(1000);
    
    // Select first patient from search results
    await page.click('[data-testid="patient-search-result-0"]');

    // Set due date
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7);
    await page.fill('[data-testid="due-date-input"]', futureDate.toISOString().split('T')[0]);

    // Submit assignment
    await page.click('[data-testid="confirm-assignment-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaire assigned successfully');
  });

  test('ClinicAdmin can review questionnaire responses', async ({ page }) => {
    // Navigate to questionnaires list
    await page.goto('/trq/questionnaires');
    await page.waitForLoadState('networkidle');

    // Look for completed questionnaires
    await page.click('[data-testid="status-filter"]');
    await page.click('[data-value="completed"]');

    // Click on first completed questionnaire
    await page.click('[data-testid="questionnaire-row-0"] [data-testid="review-button"]');

    // Verify review page
    await page.waitForURL('**/questionnaires/review/*');
    await expect(page.locator('[data-testid="questionnaire-responses"]')).toBeVisible();
    await expect(page.locator('[data-testid="patient-info"]')).toBeVisible();
    
    // Add review comments
    await page.fill('[data-testid="review-comments"]', 'Responses look good. No immediate concerns.');
    
    // Mark as reviewed
    await page.click('[data-testid="mark-reviewed-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });

  test('DELETE - ClinicAdmin can delete questionnaire', async ({ page }) => {
    // Navigate to questionnaires list
    await page.goto('/trq/questionnaires');
    await page.waitForLoadState('networkidle');

    // Search for test questionnaire
    await page.fill('[data-testid="search-input"]', testQuestionnaire.title);
    await page.waitForTimeout(1000);

    // Select questionnaire for deletion
    await page.click(`[data-testid="questionnaire-row-${testQuestionnaire.title}"] [data-testid="select-checkbox"]`);

    // Click delete button
    await page.click('[data-testid="delete-selected-button"]');

    // Confirm deletion in dialog
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Questionnaire deleted successfully');

    // Verify questionnaire no longer appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testQuestionnaire.title}`)).not.toBeVisible();
  });
});