import { test, expect } from '@playwright/test';

test.describe('Admin - Client Management CRUD', () => {
  const testClient = {
    companyName: 'Test Company LLC',
    contactName: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    address: '123 Test Street',
    city: 'Test City',
    state: 'Test State',
    postalCode: '12345',
    country: 'United States'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Admin
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/admin/home');
  });

  test('CREATE - Admin can create new client', async ({ page }) => {
    // Navigate to clients list
    await page.goto('/trq/clients');
    await page.waitForLoadState('networkidle');

    // Click Add Client button
    await page.click('[data-testid="add-client-button"]');
    await page.waitForSelector('[data-testid="add-client-dialog"]');

    // Fill client form
    await page.fill('[data-testid="company-name-input"]', testClient.companyName);
    await page.fill('[data-testid="contact-name-input"]', testClient.contactName);
    await page.fill('[data-testid="email-input"]', testClient.email);
    await page.fill('[data-testid="phone-input"]', testClient.phone);
    await page.fill('[data-testid="address-input"]', testClient.address);
    await page.fill('[data-testid="city-input"]', testClient.city);
    await page.fill('[data-testid="state-input"]', testClient.state);
    await page.fill('[data-testid="postal-code-input"]', testClient.postalCode);
    await page.fill('[data-testid="country-input"]', testClient.country);

    // Submit form
    await page.click('[data-testid="submit-client-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Client added successfully');

    // Verify client appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testClient.companyName}`)).toBeVisible();
  });

  test('READ - Admin can view client details', async ({ page }) => {
    // Navigate to clients list
    await page.goto('/trq/clients');
    await page.waitForLoadState('networkidle');

    // Search for test client
    await page.fill('[data-testid="search-input"]', testClient.companyName);
    await page.waitForTimeout(1000);

    // Click on client to view details
    await page.click(`[data-testid="client-row-${testClient.companyName}"] [data-testid="view-button"]`);
    
    // Verify client details page
    await page.waitForURL('**/clients/*/details');
    await expect(page.locator('[data-testid="client-details-company"]')).toContainText(testClient.companyName);
    await expect(page.locator('[data-testid="client-details-contact"]')).toContainText(testClient.contactName);
    await expect(page.locator('[data-testid="client-details-email"]')).toContainText(testClient.email);
    await expect(page.locator('[data-testid="client-details-phone"]')).toContainText(testClient.phone);
  });

  test('UPDATE - Admin can edit client information', async ({ page }) => {
    // Navigate to clients list
    await page.goto('/trq/clients');
    await page.waitForLoadState('networkidle');

    // Search for test client
    await page.fill('[data-testid="search-input"]', testClient.companyName);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="client-row-${testClient.companyName}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/clients/*/edit');

    // Update client information
    const updatedCompanyName = 'Updated Test Company LLC';
    await page.fill('[data-testid="company-name-input"]', updatedCompanyName);

    // Save changes
    await page.click('[data-testid="save-client-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Client updated successfully');

    // Verify changes in client details
    await expect(page.locator('[data-testid="client-details-company"]')).toContainText(updatedCompanyName);
  });

  test('DELETE - Admin can delete client', async ({ page }) => {
    // Navigate to clients list
    await page.goto('/trq/clients');
    await page.waitForLoadState('networkidle');

    // Search for test client
    await page.fill('[data-testid="search-input"]', testClient.companyName);
    await page.waitForTimeout(1000);

    // Select client for deletion
    await page.click(`[data-testid="client-row-${testClient.companyName}"] [data-testid="select-checkbox"]`);

    // Click delete button
    await page.click('[data-testid="delete-selected-button"]');

    // Confirm deletion in dialog
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Client deleted successfully');

    // Verify client no longer appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testClient.companyName}`)).not.toBeVisible();
  });

  test('Admin can manage client patients', async ({ page }) => {
    // Navigate to client details
    await page.goto('/trq/clients');
    await page.waitForLoadState('networkidle');
    
    // Find and click on client
    await page.fill('[data-testid="search-input"]', testClient.companyName);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="client-row-${testClient.companyName}"] [data-testid="view-button"]`);

    // Navigate to patients section
    await page.click('[data-testid="patients-tab"]');

    // Add patient to client
    await page.click('[data-testid="add-patient-button"]');
    await page.waitForSelector('[data-testid="add-patient-dialog"]');

    // Fill patient form
    await page.fill('[data-testid="patient-first-name"]', 'Test');
    await page.fill('[data-testid="patient-last-name"]', 'Patient');
    await page.fill('[data-testid="patient-email"]', '<EMAIL>');

    // Submit
    await page.click('[data-testid="submit-patient-button"]');

    // Verify patient added
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
  });
});