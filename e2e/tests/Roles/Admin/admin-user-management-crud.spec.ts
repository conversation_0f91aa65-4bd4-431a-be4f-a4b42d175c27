import { test, expect } from '@playwright/test';

test.describe('Admin - User Management CRUD', () => {
  const testUser = {
    firstName: 'Test',
    lastName: 'User',
    username: 'testuser_admin_crud',
    email: '<EMAIL>',
    role: 'Patient'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Admin
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/admin/home');
  });

  test('CREATE - Admin can create new user', async ({ page }) => {
    // Navigate to users list
    await page.goto('/trq/users');
    await page.waitForLoadState('networkidle');

    // Click Add User button
    await page.click('[data-testid="add-user-button"]');
    await page.waitForSelector('[data-testid="add-user-dialog"]');

    // Fill user form
    await page.fill('[data-testid="first-name-input"]', testUser.firstName);
    await page.fill('[data-testid="last-name-input"]', testUser.lastName);
    await page.fill('[data-testid="username-input"]', testUser.username);
    await page.fill('[data-testid="email-input"]', testUser.email);
    
    // Select role
    await page.click('[data-testid="role-select"]');
    await page.click(`[data-value="${testUser.role}"]`);

    // Submit form
    await page.click('[data-testid="submit-user-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('User added successfully');

    // Verify user appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testUser.email}`)).toBeVisible();
  });

  test('READ - Admin can view user details', async ({ page }) => {
    // Navigate to users list
    await page.goto('/trq/users');
    await page.waitForLoadState('networkidle');

    // Search for test user
    await page.fill('[data-testid="search-input"]', testUser.email);
    await page.waitForTimeout(1000); // Wait for search debounce

    // Click on user to view details
    await page.click(`[data-testid="user-row-${testUser.email}"] [data-testid="view-button"]`);
    
    // Verify user details page
    await page.waitForURL('**/users/*/details');
    await expect(page.locator('[data-testid="user-details-name"]')).toContainText(`${testUser.firstName} ${testUser.lastName}`);
    await expect(page.locator('[data-testid="user-details-email"]')).toContainText(testUser.email);
    await expect(page.locator('[data-testid="user-details-role"]')).toContainText(testUser.role);
  });

  test('UPDATE - Admin can edit user information', async ({ page }) => {
    // Navigate to users list
    await page.goto('/trq/users');
    await page.waitForLoadState('networkidle');

    // Search for test user
    await page.fill('[data-testid="search-input"]', testUser.email);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="user-row-${testUser.email}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/users/*/edit');

    // Update user information
    const updatedName = 'Updated';
    await page.fill('[data-testid="first-name-input"]', updatedName);

    // Save changes
    await page.click('[data-testid="save-user-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('User updated successfully');

    // Verify changes in user details
    await expect(page.locator('[data-testid="user-details-name"]')).toContainText(`${updatedName} ${testUser.lastName}`);
  });

  test('DELETE - Admin can delete user', async ({ page }) => {
    // Navigate to users list
    await page.goto('/trq/users');
    await page.waitForLoadState('networkidle');

    // Search for test user
    await page.fill('[data-testid="search-input"]', testUser.email);
    await page.waitForTimeout(1000);

    // Select user for deletion
    await page.click(`[data-testid="user-row-${testUser.email}"] [data-testid="select-checkbox"]`);

    // Click delete button
    await page.click('[data-testid="delete-selected-button"]');

    // Confirm deletion in dialog
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('User deleted successfully');

    // Verify user no longer appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testUser.email}`)).not.toBeVisible();
  });

  test('Admin can bulk delete users', async ({ page }) => {
    // Create multiple test users first
    const testUsers = [
      { ...testUser, email: '<EMAIL>', username: 'bulk1' },
      { ...testUser, email: '<EMAIL>', username: 'bulk2' }
    ];

    // Navigate to users list
    await page.goto('/trq/users');
    await page.waitForLoadState('networkidle');

    // Create test users via API or UI (simplified here)
    // In real implementation, you'd create these users first

    // Select multiple users
    for (const user of testUsers) {
      await page.click(`[data-testid="user-row-${user.email}"] [data-testid="select-checkbox"]`);
    }

    // Delete selected users
    await page.click('[data-testid="delete-selected-button"]');
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});