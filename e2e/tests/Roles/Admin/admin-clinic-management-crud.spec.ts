import { test, expect } from '@playwright/test';

test.describe('Admin - Clinic Management CRUD', () => {
  const testClinic = {
    name: 'Test Medical Clinic',
    contactName: 'Dr. <PERSON>',
    email: '<EMAIL>',
    phone: '******-0456',
    address: '456 Medical Drive',
    city: 'Healthcare City',
    state: 'Medical State',
    postalCode: '54321',
    country: 'United States'
  };

  test.beforeEach(async ({ page }) => {
    // Login as Admin
    await page.goto('/trq/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('**/trq/admin/home');
  });

  test('CREATE - Admin can create new clinic', async ({ page }) => {
    // Navigate to clinics list
    await page.goto('/trq/clinics');
    await page.waitForLoadState('networkidle');

    // Click Add Clinic button
    await page.click('[data-testid="add-clinic-button"]');
    await page.waitForSelector('[data-testid="add-clinic-dialog"]');

    // Fill clinic form
    await page.fill('[data-testid="clinic-name-input"]', testClinic.name);
    await page.fill('[data-testid="contact-name-input"]', testClinic.contactName);
    await page.fill('[data-testid="email-input"]', testClinic.email);
    await page.fill('[data-testid="phone-input"]', testClinic.phone);
    await page.fill('[data-testid="address-input"]', testClinic.address);
    await page.fill('[data-testid="city-input"]', testClinic.city);
    await page.fill('[data-testid="state-input"]', testClinic.state);
    await page.fill('[data-testid="postal-code-input"]', testClinic.postalCode);
    await page.fill('[data-testid="country-input"]', testClinic.country);

    // Submit form
    await page.click('[data-testid="submit-clinic-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Clinic added successfully');

    // Verify clinic appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testClinic.name}`)).toBeVisible();
  });

  test('READ - Admin can view clinic details', async ({ page }) => {
    // Navigate to clinics list
    await page.goto('/trq/clinics');
    await page.waitForLoadState('networkidle');

    // Search for test clinic
    await page.fill('[data-testid="search-input"]', testClinic.name);
    await page.waitForTimeout(1000);

    // Click on clinic to view details
    await page.click(`[data-testid="clinic-row-${testClinic.name}"] [data-testid="view-button"]`);
    
    // Verify clinic details page
    await page.waitForURL('**/clinics/*/details');
    await expect(page.locator('[data-testid="clinic-details-name"]')).toContainText(testClinic.name);
    await expect(page.locator('[data-testid="clinic-details-contact"]')).toContainText(testClinic.contactName);
    await expect(page.locator('[data-testid="clinic-details-email"]')).toContainText(testClinic.email);
    await expect(page.locator('[data-testid="clinic-details-phone"]')).toContainText(testClinic.phone);
  });

  test('UPDATE - Admin can edit clinic information', async ({ page }) => {
    // Navigate to clinics list
    await page.goto('/trq/clinics');
    await page.waitForLoadState('networkidle');

    // Search for test clinic
    await page.fill('[data-testid="search-input"]', testClinic.name);
    await page.waitForTimeout(1000);

    // Click edit button
    await page.click(`[data-testid="clinic-row-${testClinic.name}"] [data-testid="edit-button"]`);
    
    // Verify edit page
    await page.waitForURL('**/clinics/*/edit');

    // Update clinic information
    const updatedName = 'Updated Test Medical Clinic';
    await page.fill('[data-testid="clinic-name-input"]', updatedName);

    // Save changes
    await page.click('[data-testid="save-clinic-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Clinic updated successfully');

    // Verify changes in clinic details
    await expect(page.locator('[data-testid="clinic-details-name"]')).toContainText(updatedName);
  });

  test('DELETE - Admin can delete clinic', async ({ page }) => {
    // Navigate to clinics list
    await page.goto('/trq/clinics');
    await page.waitForLoadState('networkidle');

    // Search for test clinic
    await page.fill('[data-testid="search-input"]', testClinic.name);
    await page.waitForTimeout(1000);

    // Select clinic for deletion
    await page.click(`[data-testid="clinic-row-${testClinic.name}"] [data-testid="select-checkbox"]`);

    // Click delete button
    await page.click('[data-testid="delete-selected-button"]');

    // Confirm deletion in dialog
    await page.waitForSelector('[data-testid="confirm-delete-dialog"]');
    await page.click('[data-testid="confirm-delete-button"]');

    // Verify success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Clinic deleted successfully');

    // Verify clinic no longer appears in list
    await page.waitForLoadState('networkidle');
    await expect(page.locator(`text=${testClinic.name}`)).not.toBeVisible();
  });

  test('Admin can manage clinic administrators', async ({ page }) => {
    // Navigate to clinic details
    await page.goto('/trq/clinics');
    await page.waitForLoadState('networkidle');
    
    // Find and click on clinic
    await page.fill('[data-testid="search-input"]', testClinic.name);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="clinic-row-${testClinic.name}"] [data-testid="view-button"]`);

    // Navigate to administrators section
    await page.click('[data-testid="administrators-tab"]');

    // Add administrator to clinic
    await page.click('[data-testid="add-administrator-button"]');
    await page.waitForSelector('[data-testid="add-administrator-dialog"]');

    // Select existing user as administrator
    await page.click('[data-testid="user-select"]');
    await page.click('[data-value="existing-user-id"]'); // This would be dynamic in real tests

    // Submit
    await page.click('[data-testid="submit-administrator-button"]');

    // Verify administrator added
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="administrators-list"]')).toContainText('Administrator added');
  });

  test('Admin can view clinic statistics', async ({ page }) => {
    // Navigate to clinic details
    await page.goto('/trq/clinics');
    await page.waitForLoadState('networkidle');
    
    // Find and click on clinic
    await page.fill('[data-testid="search-input"]', testClinic.name);
    await page.waitForTimeout(1000);
    await page.click(`[data-testid="clinic-row-${testClinic.name}"] [data-testid="view-button"]`);

    // Check statistics are displayed
    await expect(page.locator('[data-testid="total-patients-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="total-doctors-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="active-questionnaires-stat"]')).toBeVisible();
    await expect(page.locator('[data-testid="compliance-rate-stat"]')).toBeVisible();
  });
});