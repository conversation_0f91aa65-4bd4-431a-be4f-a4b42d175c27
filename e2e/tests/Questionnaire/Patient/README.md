# Patient Questionnaire E2E Tests

This directory contains end-to-end tests for patient questionnaire completion workflows.

## Test Files

### `patient-questionnaire-completion.spec.ts`
Comprehensive test covering the complete patient journey:
- Patient login and authentication
- Navigation to assigned questionnaires
- Opening and completing respiratory questionnaire
- Form validation and submission
- Completion confirmation and redirect

## Page Object Models

The tests use the following page object models located in `e2e/pages/`:

- `PatientDashboardPage` - Patient home dashboard with questionnaire cards
- `PatientQuestionnaireListPage` - <PERSON><PERSON>'s questionnaire list view
- `RespiratoryQuestionnaireWizardPage` - Respiratory questionnaire completion wizard

## Test Utilities

### `patient-test-data-setup.ts`
Utility functions for:
- Creating test patient users
- Assigning respiratory questionnaires to patients
- Setting up test data before tests
- Cleaning up test data after tests

## Test Flow

1. **Setup Phase**:
   - Create test patient user
   - Admin/provider assigns respiratory questionnaire to patient
   - Verify questionnaire assignment

2. **Patient Flow**:
   - Patient logs in to their account
   - <PERSON><PERSON> navigates to questionnaires from dashboard
   - <PERSON><PERSON> opens assigned respiratory questionnaire
   - <PERSON><PERSON> completes all required form sections:
     - Terms & Employee Information
     - General Health Information
     - Additional Questions
     - PLHCP Questions
     - Review & Submit
   - Patient submits completed questionnaire

3. **Verification**:
   - Verify successful submission
   - Verify appropriate confirmation message
   - Verify redirect to appropriate page
   - Verify questionnaire status updated to completed

## Running Tests

### Using npm scripts:

```bash
# Run all patient questionnaire tests
npm run e2e:questionnaires:patient

# Run specific test file
npm run test:e2e:questionnaire:patient:completion
```

### Using Playwright directly:

```bash
# Run all patient tests
npx playwright test e2e/tests/Questionnaire/Patient/

# Run specific test file
npx playwright test e2e/tests/Questionnaire/Patient/patient-questionnaire-completion.spec.ts

# Run with browser visible
npx playwright test e2e/tests/Questionnaire/Patient/ --headed

# Run in debug mode
npx playwright test e2e/tests/Questionnaire/Patient/ --debug
```

## Test Data

Tests use the following test data:
- Test patient: `<EMAIL>`
- Respiratory questionnaire template: `respiratory-health-questionnaire-template`
- Test responses for all required fields

## Performance Optimizations

- Reuses login sessions when possible to improve test performance
- Uses page object models for maintainable test code
- Implements proper wait strategies for dynamic content
- Optimizes form filling with efficient selectors

## Notes

- Tests are designed to run individually without dependencies on other test suites
- Each test includes proper setup and teardown
- Tests validate both happy path and error scenarios
- All tests follow the existing e2e testing patterns and conventions
