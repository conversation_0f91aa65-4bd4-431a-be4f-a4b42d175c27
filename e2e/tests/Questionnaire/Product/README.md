# Product Questionnaire E2E Tests

This directory contains end-to-end tests for the complete respiratory questionnaire product purchase and assignment workflow.

## Test Files

### `product-purchase-workflow.spec.ts`
Comprehensive test covering the complete business flow:
- Client product purchase flow
- Client assignment flow  
- Patient completion flow

### `client-product-purchase.spec.ts`
Focused test for client product purchase:
- Login as Client role user
- Navigate to product marketplace
- Purchase respiratory questionnaire product
- Verify successful purchase and product availability

### `client-product-assignment.spec.ts`
Focused test for client product assignment:
- Access purchased questionnaire products
- Assign respiratory questionnaire to test patient
- Verify successful assignment with notifications

## Page Object Models

The tests use the following page object models:

### Client Purchase Flow
- `ClientProductMarketplacePage` - Product marketplace navigation and browsing
- `RespiratoryProductDetailsPage` - Respiratory questionnaire product details and purchase
- `ClientPurchaseConfirmationPage` - Purchase confirmation and receipt

### Client Assignment Flow  
- `ClientDashboardPage` - Client dashboard with purchased products
- `ClientProductManagementPage` - Purchased product management interface
- `ClientAssignmentDialogPage` - Patient assignment dialog and workflow

### Patient Completion Flow
- `PatientDashboardPage` - Patient dashboard with assigned questionnaires (reused)
- `RespiratoryQuestionnaireWizardPage` - Questionnaire completion wizard (reused)

## Test Utilities

### `product-test-data-setup.ts`
Utility functions for:
- Creating test client users
- Setting up respiratory questionnaire products
- Managing product purchases
- Creating patient assignments
- Cleaning up test data after tests

## Test Flow Overview

### 1. Client Product Purchase Flow
```
Client Login → Product Marketplace → Respiratory Product → Purchase → Confirmation
```

### 2. Client Assignment Flow  
```
Client Dashboard → Purchased Products → Select Product → Assign to Patient → Verification
```

### 3. Patient Completion Flow
```
Patient Login → Assigned Questionnaires → Open Questionnaire → Complete → Submit
```

### 4. Complete Business Workflow
```
Client Purchase → Client Assignment → Patient Completion → Data Verification
```

## Verification Points

- **Product Purchase**: Purchase confirmation, billing, product availability in client account
- **Product Assignment**: Successful assignment, patient notification, assignment status
- **Patient Access**: Patient can see and access assigned questionnaire
- **Questionnaire Completion**: Complete submission, data persistence, status updates
- **Business Workflow**: End-to-end data flow and status tracking

## Running Tests

### Using npm scripts:

```bash
# Run all product workflow tests
npm run e2e:questionnaire:product

# Run specific test files
npm run test:e2e:questionnaire:product:purchase
npm run test:e2e:questionnaire:product:assignment
npm run test:e2e:questionnaire:product:workflow
```

### Using Playwright directly:

```bash
# Run all product tests
npx playwright test e2e/tests/Questionnaire/Product/

# Run specific test file
npx playwright test e2e/tests/Questionnaire/Product/product-purchase-workflow.spec.ts

# Run with browser visible
npx playwright test e2e/tests/Questionnaire/Product/ --headed

# Run in debug mode
npx playwright test e2e/tests/Questionnaire/Product/ --debug
```

## Test Data

Tests use the following test data:
- Test client: `<EMAIL>`
- Test patient: `<EMAIL>` 
- Respiratory questionnaire product: `respiratory-health-questionnaire-product`
- Test purchase and assignment data

## Performance Optimizations

- Reuses login sessions when possible to improve test performance
- Uses page object models for maintainable test code
- Implements proper wait strategies for dynamic content
- Optimizes form filling with efficient selectors
- Includes proper test data cleanup

## Notes

- Tests demonstrate the complete commercial workflow from product purchase through patient completion
- Validates the questionnaire-as-a-product business model
- Tests are designed to run individually without dependencies on other test suites
- Each test includes proper setup and teardown
- Tests validate both happy path and error scenarios
- All tests follow the existing e2e testing patterns and conventions
