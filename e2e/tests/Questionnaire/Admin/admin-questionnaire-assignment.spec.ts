import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role'; // Correct import for Role enum
import { AllQuestionnairesPage } from '../../../pages/Questionnaire/all-questionnaires.page';
import { CreateQuestionnairePage } from '../../../pages/Questionnaire/create-questionnaire.page';
import { AssignQuestionnaireDialog } from '../../../pages/Questionnaire/assign-questionnaire.dialog';
import { TemplateSelectionDialog } from '../../../pages/Questionnaire/template-selection.dialog';

test.describe('Admin Questionnaire Management', () => {
  let allQuestionnairesPage: AllQuestionnairesPage;
  let createQuestionnairePage: CreateQuestionnairePage;
  let assignDialog: AssignQuestionnaireDialog;
  let templateDialog: TemplateSelectionDialog;

  test.beforeEach(async ({ page, baseURL }) => {
    // Instantiate POMs
    allQuestionnairesPage = new AllQuestionnairesPage(page);
    createQuestionnairePage = new CreateQuestionnairePage(page);
    assignDialog = new AssignQuestionnaireDialog(page);
    templateDialog = new TemplateSelectionDialog(page);

    // Log in as an admin before each test
    await loginAs(page, Role.Admin, baseURL);

    // Navigate to questionnaires page after login
    await allQuestionnairesPage.goto();
  });

  test('should allow an admin to create a new questionnaire', async () => {
    // 1. Navigate to questionnaire creation page
    await createQuestionnairePage.goto();

    // 2. Fill in questionnaire details
    const questionnaireName = `E2E Test Name ${Date.now()}`;
    const questionnaireTitle = `E2E Test Title ${Date.now()}`;
    const questionnaireDescription = 'This is a test description for the questionnaire created via E2E test.';
    await createQuestionnairePage.fillDetails(questionnaireName, questionnaireTitle, questionnaireDescription);

    // 3. Save the questionnaire
    await createQuestionnairePage.save();

    // 4. Assert that the questionnaire was created successfully
    await createQuestionnairePage.verifySuccess();

    // 5. Verify the questionnaire appears in the list
    const newRow = allQuestionnairesPage.getQuestionnaireRow(questionnaireTitle);
    await expect(newRow).toBeVisible({ timeout: 10000 });

    // 6. Verify it has the correct status
    await allQuestionnairesPage.verifyQuestionnaireCreated(newRow);
  });

  test('should allow an admin to assign a questionnaire to a patient', async ({ page }) => {
    // First create a questionnaire to assign
    await createQuestionnairePage.goto();
    const questionnaireName = `Assignment Test ${Date.now()}`;
    const questionnaireTitle = `Assignment Test Title ${Date.now()}`;
    const questionnaireDescription = 'This questionnaire will be assigned to a patient.';
    await createQuestionnairePage.fillDetails(questionnaireName, questionnaireTitle, questionnaireDescription);
    await createQuestionnairePage.save();
    await createQuestionnairePage.verifySuccess();

    // Now we're back on the questionnaires list page
    // Find the questionnaire row
    const questionnaireRow = allQuestionnairesPage.getQuestionnaireRow(questionnaireTitle);
    await expect(questionnaireRow).toBeVisible({ timeout: 10000 });

    // Click the assign button for the row to open the dialog
    await allQuestionnairesPage.clickAssignForRow(questionnaireRow);

    // Wait for the dialog and search for a patient
    await assignDialog.waitForDialog();

    // Search for a test patient (assuming there are test patients in the system)
    await assignDialog.searchForPatient('test');

    // Select the first available patient (we'll use a generic approach)
    const firstPatientCard = page.locator('[data-testid="assign-questionnaire-dialog"] .MuiCard-root').first();
    await expect(firstPatientCard).toBeVisible({ timeout: 10000 });
    await firstPatientCard.click();

    // Submit the assignment
    await assignDialog.submit();

    // Verify the questionnaire appears to be assigned (status should change)
    const updatedQuestionnaireRow = allQuestionnairesPage.getQuestionnaireRow(questionnaireTitle);
    await expect(updatedQuestionnaireRow).toBeVisible();

    // Check that the status is no longer "created" (it should be "assigned")
    const statusCell = updatedQuestionnaireRow.locator('.MuiDataGrid-cell[data-field="status"] .MuiChip-label');
    await expect(statusCell).not.toHaveText('created');
  });

  test('should allow an admin to create and then assign a questionnaire', async ({ page }) => {
    const newQuestionnaireName = `E2E Full Flow ${Date.now()}`;
    const newQuestionnaireTitle = `E2E Full Flow Title ${Date.now()}`;
    const newQuestionnaireDescription = 'Created and assigned via E2E test.';

    // --- Create Questionnaire ---
    await createQuestionnairePage.goto();
    await createQuestionnairePage.fillDetails(newQuestionnaireName, newQuestionnaireTitle, newQuestionnaireDescription);
    await createQuestionnairePage.save();
    await createQuestionnairePage.verifySuccess(); // Lands on AllQuestionnairesPage

    // --- Assign Questionnaire ---
    // Find the newly created questionnaire row
    const questionnaireRow = allQuestionnairesPage.getQuestionnaireRow(newQuestionnaireTitle);
    await expect(questionnaireRow).toBeVisible({ timeout: 10000 });

    // Click assign button for the row
    await allQuestionnairesPage.clickAssignForRow(questionnaireRow);

    // Wait for dialog, search and select patient
    await assignDialog.waitForDialog();
    await assignDialog.searchForPatient('test');

    // Select the first available patient
    const firstPatientCard = page.locator('[data-testid="assign-questionnaire-dialog"] .MuiCard-root').first();
    await expect(firstPatientCard).toBeVisible({ timeout: 10000 });
    await firstPatientCard.click();

    // Confirm assignment in dialog
    await assignDialog.submit();

    // Assert grid updated - check that status is no longer "created"
    const updatedQuestionnaireRow = allQuestionnairesPage.getQuestionnaireRow(newQuestionnaireTitle);
    await expect(updatedQuestionnaireRow).toBeVisible();
    const statusCell = updatedQuestionnaireRow.locator('.MuiDataGrid-cell[data-field="status"] .MuiChip-label');
    await expect(statusCell).not.toHaveText('created');
  });

  test('should allow an admin to create a questionnaire from a template', async () => {
    // 1. Click the "Create from Template" button
    await allQuestionnairesPage.clickCreateFromTemplate();

    // 2. Wait for the template selection dialog
    await templateDialog.waitForDialog();

    // 3. Select a template (e.g., General Health Assessment)
    const templateId = 'general-health'; // ID of the template to select
    await templateDialog.selectTemplate(templateId);

    // 4. Click the "Create Questionnaire" button in the dialog
    await templateDialog.submit();

    // 5. Wait for the dialog to close and page to refresh
    await allQuestionnairesPage.page.waitForTimeout(3000);

    // 6. Assert that a new questionnaire appears in the list
    // Look for a questionnaire with a title that contains "General Health" or similar
    const newQuestionnaireRow = allQuestionnairesPage.page.locator('.MuiDataGrid-row').first();
    await expect(newQuestionnaireRow).toBeVisible({ timeout: 10000 });

    // Verify it has the "created" status (case insensitive)
    const statusCell = newQuestionnaireRow.locator('.MuiDataGrid-cell[data-field="status"] .MuiChip-label');
    await expect(statusCell).toHaveText(/created/i);
  });

  test('should allow an admin to create and delete a questionnaire', async () => {
    // --- Create Questionnaire ---
    await createQuestionnairePage.goto();
    const questionnaireName = `E2E Delete Test ${Date.now()}`;
    const questionnaireTitle = `E2E Delete Test Title ${Date.now()}`;
    const questionnaireDescription = 'This questionnaire will be deleted.';
    await createQuestionnairePage.fillDetails(questionnaireName, questionnaireTitle, questionnaireDescription);
    await createQuestionnairePage.save();
    await createQuestionnairePage.verifySuccess(); // Lands on AllQuestionnairesPage

    // --- Delete Questionnaire ---
    // Select the row of the newly created questionnaire
    await allQuestionnairesPage.selectRowByTitle(questionnaireTitle);

    // Click the delete button in the toolbar
    await allQuestionnairesPage.clickToolbarDelete();

    // Confirm the deletion in the dialog
    await allQuestionnairesPage.confirmDeletion();

    // Verify the questionnaire is no longer in the list
    await allQuestionnairesPage.verifyQuestionnaireDeleted(questionnaireTitle);
  });

  test('should allow an admin to access the respiratory questionnaire', async () => {
    // Click the respiratory questionnaire button
    await allQuestionnairesPage.clickRespiratoryQuestionnaire();

    // Verify navigation to the respiratory questionnaire
    await expect(allQuestionnairesPage.page).toHaveURL('/trq/questionnaires/respiratory', { timeout: 10000 });

    // Verify the respiratory questionnaire page loads
    await expect(allQuestionnairesPage.page.locator('h4:has-text("Respiratory Health Questionnaire")')).toBeVisible({ timeout: 10000 });
  });
});
