import { test, expect } from '@playwright/test';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../pages/Templates/all-templates.page';
import { TemplateDetailsPage } from '../../pages/Templates/template-details.page';

test.describe('Debug Respiratory Health Template', () => {
  let allTemplatesPage: AllTemplatesPage;
  let templateDetailsPage: TemplateDetailsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    allTemplatesPage = new AllTemplatesPage(page);
    templateDetailsPage = new TemplateDetailsPage(page);
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should inspect Respiratory Health Questionnaire template details', async ({ page }) => {
    // Navigate to templates page
    await allTemplatesPage.goto();
    await allTemplatesPage.waitForPageLoad();

    // Verify the template exists
    const templateExists = await allTemplatesPage.verifyTemplateExists('Respiratory Health Questionnaire');
    expect(templateExists).toBe(true);
    console.log('✅ Respiratory Health Questionnaire template exists');

    // Verify it's published
    const isPublished = await allTemplatesPage.verifyTemplatePublished('Respiratory Health Questionnaire');
    expect(isPublished).toBe(true);
    console.log('✅ Respiratory Health Questionnaire is published');

    // Click on the template to view details
    await allTemplatesPage.clickTemplateByTitle('Respiratory Health Questionnaire');
    
    // Wait for template details page to load
    await page.waitForTimeout(3000);
    
    // Get the current URL to see if we're on the details page
    const currentUrl = page.url();
    console.log('Current URL after clicking template:', currentUrl);

    // Try to get template information from the page
    const templateTitle = await page.locator('h1, h2, h3, h4, h5, h6').first().textContent();
    console.log('Template title on details page:', templateTitle);

    // Check if there's a publish/unpublish button
    const publishButton = page.getByRole('button', { name: /publish|unpublish/i });
    const publishButtonExists = await publishButton.isVisible();
    console.log('Publish/Unpublish button visible:', publishButtonExists);

    if (publishButtonExists) {
      const buttonText = await publishButton.textContent();
      console.log('Publish button text:', buttonText);
    }

    // Check for any error messages on the page
    const errorMessages = await page.locator('.MuiAlert-root, .error, [role="alert"]').allTextContents();
    if (errorMessages.length > 0) {
      console.log('Error messages on page:', errorMessages);
    }

    // Try to manually trigger the sync by unpublishing and republishing
    if (publishButtonExists) {
      const buttonText = await publishButton.textContent();
      
      if (buttonText?.toLowerCase().includes('unpublish')) {
        console.log('🔧 Template is published - trying unpublish/republish cycle...');
        
        // Unpublish
        await publishButton.click();
        await page.waitForTimeout(3000);
        
        // Check if button changed to "Publish"
        const newButtonText = await publishButton.textContent();
        console.log('Button text after unpublish:', newButtonText);
        
        // Republish
        if (newButtonText?.toLowerCase().includes('publish')) {
          await publishButton.click();
          await page.waitForTimeout(3000);
          
          const finalButtonText = await publishButton.textContent();
          console.log('Button text after republish:', finalButtonText);
          console.log('✅ Completed unpublish/republish cycle');
        }
      }
    }

    // Listen for any console errors during the process
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`Browser error: ${msg.text()}`);
      }
    });
  });

  test('should try to manually create product for Respiratory Health template', async ({ page }) => {
    // This test will use browser console to manually trigger product creation
    await allTemplatesPage.goto();
    await allTemplatesPage.waitForPageLoad();

    // Execute JavaScript in browser to manually create product
    const result = await page.evaluate(async () => {
      try {
        // Get all templates
        const { getTemplates } = await import('/src/Questionnaires/Templates/[services]/questionnaireTemplateService');
        const templates = await getTemplates();
        
        // Find Respiratory Health template
        const respiratoryTemplate = templates.find(t => 
          t.name?.includes('Respiratory Health') || 
          t.title?.includes('Respiratory Health')
        );
        
        if (!respiratoryTemplate) {
          return { success: false, error: 'Template not found' };
        }
        
        console.log('Found template:', respiratoryTemplate);
        
        // Try to create product manually
        const { syncProductOnTemplatePublish } = await import('/src/Products/[services]/templateProductSyncService');
        const product = await syncProductOnTemplatePublish(respiratoryTemplate.id);
        
        return { success: true, product, templateId: respiratoryTemplate.id };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    console.log('Manual product creation result:', result);
    
    if (result.success) {
      console.log('🎉 Successfully created product manually!');
    } else {
      console.log('❌ Failed to create product:', result.error);
    }
  });
});
