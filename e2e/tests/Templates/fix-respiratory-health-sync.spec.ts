import { test, expect } from '@playwright/test';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../pages/Templates/all-templates.page';
import { ProductsPage } from '../../pages/Products/products.page';

test.describe('Fix Respiratory Health Questionnaire Sync', () => {
  let allTemplatesPage: AllTemplatesPage;
  let productsPage: ProductsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    allTemplatesPage = new AllTemplatesPage(page);
    productsPage = new ProductsPage(page);
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should fix Respiratory Health Questionnaire sync by running migration', async ({ page }) => {
    // Step 1: Verify the template exists and is published
    await allTemplatesPage.goto();
    await allTemplatesPage.waitForPageLoad();
    
    const respiratoryTemplateExists = await allTemplatesPage.verifyTemplateExists('Respiratory Health Questionnaire');
    expect(respiratoryTemplateExists).toBe(true);
    
    const isPublished = await allTemplatesPage.verifyTemplatePublished('Respiratory Health Questionnaire');
    expect(isPublished).toBe(true);
    
    console.log('✅ Respiratory Health Questionnaire template exists and is published');

    // Step 2: Check if it exists as a product (should be missing)
    await productsPage.goto();
    await productsPage.waitForPageLoad();
    
    let respiratoryProductExists = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
    console.log('Respiratory Health Questionnaire exists as product (before fix):', respiratoryProductExists);

    // Step 3: If product doesn't exist, run the sync fix
    if (!respiratoryProductExists) {
      console.log('🔧 Product missing - running sync fix...');
      
      // Execute the sync fix using browser console
      await page.evaluate(async () => {
        // Import and run the fix function
        const { fixRespiratoryHealthSync } = await import('/src/utils/fixRespiratoryHealthSync.ts');
        const result = await fixRespiratoryHealthSync();
        console.log('Sync fix result:', result);
        return result;
      });
      
      // Wait for sync to complete
      await page.waitForTimeout(5000);
      
      // Reload the products page
      await page.reload();
      await productsPage.waitForPageLoad();
      
      // Check if product now exists
      respiratoryProductExists = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
      console.log('Respiratory Health Questionnaire exists as product (after fix):', respiratoryProductExists);
      
      expect(respiratoryProductExists).toBe(true);
    }

    console.log('✅ Respiratory Health Questionnaire sync fix completed successfully');
  });

  test('should run full template sync to ensure all published templates have products', async ({ page }) => {
    // Step 1: Get count of published templates
    await allTemplatesPage.goto();
    await allTemplatesPage.waitForPageLoad();
    
    const publishedTemplatesCount = await allTemplatesPage.getPublishedTemplatesCount();
    console.log('Published templates count:', publishedTemplatesCount);

    // Step 2: Get count of products
    await productsPage.goto();
    await productsPage.waitForPageLoad();
    
    const productsCount = await productsPage.getProductCount();
    console.log('Products count (before sync):', productsCount);

    // Step 3: Run full sync if counts don't match
    if (publishedTemplatesCount !== productsCount) {
      console.log('🔧 Template and product counts don\'t match - running full sync...');
      
      // Execute the full sync using browser console
      await page.evaluate(async () => {
        const { runFullTemplateSync } = await import('/src/utils/fixRespiratoryHealthSync.ts');
        const result = await runFullTemplateSync();
        console.log('Full sync result:', result);
        return result;
      });
      
      // Wait for sync to complete
      await page.waitForTimeout(10000);
      
      // Reload the products page
      await page.reload();
      await productsPage.waitForPageLoad();
      
      const productsCountAfterSync = await productsPage.getProductCount();
      console.log('Products count (after sync):', productsCountAfterSync);
      
      // Verify that we now have products for all published templates
      expect(productsCountAfterSync).toBeGreaterThanOrEqual(publishedTemplatesCount);
    }

    console.log('✅ Full template sync completed successfully');
  });
});
