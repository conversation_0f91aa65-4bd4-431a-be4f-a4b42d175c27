# Template Publishing E2E Tests

This directory contains end-to-end tests for template creation, management, and publishing workflows for both Admin and Clinic Admin users.

## Overview

These tests cover the complete workflow of:
1. Creating questionnaire templates
2. Publishing templates to make them available as products
3. Verifying published templates appear in the products marketplace
4. Managing template lifecycle (publish/unpublish)
5. Role-based access control for template management

## Test Structure

```
e2e/tests/Templates/
├── Admin/
│   └── admin-template-publishing.spec.ts
├── ClinicAdmin/
│   └── clinic-admin-template-publishing.spec.ts
└── README.md (this file)
```

## Page Object Models

The tests use the following page object models located in `e2e/pages/Templates/`:

- `AllTemplatesPage` - Template listing and management page
- `CreateTemplatePage` - Template creation and editing forms
- `TemplateDetailsPage` - Template details view with publish/unpublish functionality

Additional page objects used:
- `ProductsPage` - Products marketplace for verifying published templates

## Test Scenarios

### Admin Template Publishing Tests (`admin-template-publishing.spec.ts`)

1. **Create and Publish Template**
   - <PERSON><PERSON> creates a new template with questions
   - Publishes the template
   - Verifies template appears as a product in marketplace

2. **Unpublish Template**
   - Creates and publishes a template
   - Unpublishes the template
   - Verifies product disappears from marketplace

3. **Template Copy and Publish**
   - Creates an original template
   - Creates a modified copy
   - Publishes only the copy
   - Verifies only published template appears as product

4. **Edit Restrictions for Published Templates**
   - Creates and publishes a template
   - Verifies edit functionality is disabled for published templates
   - Unpublishes and verifies edit functionality returns

### Clinic Admin Template Publishing Tests (`clinic-admin-template-publishing.spec.ts`)

1. **Clinic Admin Template Creation and Publishing**
   - Clinic admin creates clinic-specific templates
   - Publishes templates with clinical assessment questions
   - Verifies templates appear as products

2. **Clinic-Specific Template Management**
   - Creates templates with clinic workflow questions
   - Manages clinic-specific template categories
   - Verifies clinic admin access to their templates

3. **Patient Intake Template Creation**
   - Creates comprehensive patient intake templates
   - Adds intake-specific questions
   - Publishes and verifies as products

4. **Republish Workflow**
   - Tests unpublish and republish functionality
   - Verifies product availability changes accordingly

5. **Permission Verification**
   - Verifies clinic admin has same publishing permissions as admin
   - Tests template management capabilities

## Running Tests

### Using npm scripts:

```bash
# Run all template tests
npm run test:e2e:templates

# Run admin template tests only
npm run test:e2e:templates:admin
npm run e2e:templates:admin

# Run clinic admin template tests only
npm run test:e2e:templates:clinic-admin
npm run e2e:templates:clinic-admin

# Run specific test files
npm run test:e2e:templates:admin:publishing
npm run test:e2e:templates:clinic-admin:publishing
```

### Using Playwright directly:

```bash
# Run all template tests
npx playwright test e2e/tests/Templates/

# Run admin tests
npx playwright test e2e/tests/Templates/Admin/

# Run clinic admin tests
npx playwright test e2e/tests/Templates/ClinicAdmin/

# Run specific test file
npx playwright test e2e/tests/Templates/Admin/admin-template-publishing.spec.ts

# Run with browser visible
npx playwright test e2e/tests/Templates/ --headed

# Run in debug mode
npx playwright test e2e/tests/Templates/ --debug
```

## Test Data

Tests create unique templates using timestamps to avoid conflicts:
- Template names include timestamps: `E2E Admin Template ${Date.now()}`
- Each test creates its own test data
- Tests are designed to be independent and can run in parallel

## Performance Optimization

These tests are optimized for performance by:
- **Persistent Login**: Login happens once per test suite using `test.beforeAll()` and storage state
- **Session Reuse**: All tests in a suite reuse the same authenticated session
- **Reduced Setup Time**: No repeated login/logout cycles between tests
- **Parallel Execution**: Tests can run in parallel within the same role

## Key Workflows Tested

### Template Creation to Product Publication Flow

```
1. Login as Admin/Clinic Admin
2. Navigate to Templates page (/trq/templates)
3. Create new template with questions
4. Save template (appears as draft)
5. Navigate to template details
6. Publish template using toggle switch
7. Navigate to Products page (/trq/products)
8. Verify template appears as purchasable product
```

### Template Unpublishing Flow

```
1. Start with published template
2. Navigate to template details
3. Unpublish using toggle switch
4. Navigate to Products page
5. Verify product no longer appears
```

## Verification Points

- **Template Creation**: Template appears in templates list with correct status
- **Template Publishing**: Publish toggle works and status updates
- **Product Appearance**: Published templates appear as products in marketplace
- **Product Removal**: Unpublished templates disappear from marketplace
- **Role Permissions**: Both Admin and Clinic Admin can publish templates
- **Edit Restrictions**: Published templates cannot be edited
- **Template Details**: Correct template information is preserved

## Dependencies

- Playwright Test Framework
- Page Object Models in `e2e/pages/Templates/` and `e2e/pages/Products/`
- Auth utilities in `e2e/utils/auth-utils.ts`
- Role definitions from `src/RBAC/[types]/Role`

## Prerequisites

1. Test environment should be set up with:
   - Admin and Clinic Admin test users
   - Clean database state
   - Template and product services running

2. Run setup scripts if needed:
   ```bash
   npm run e2e:reset-all
   ```

## Troubleshooting

### Common Issues

1. **Template not appearing as product**: 
   - Check if template publishing service is running
   - Verify template has required fields filled
   - Check browser network tab for API errors

2. **Permission errors**:
   - Verify test user has correct role (Admin or ClinicAdmin)
   - Check RBAC configuration

3. **Timing issues**:
   - Tests include appropriate waits for async operations
   - Increase timeouts if needed for slower environments

### Debug Tips

- Use `--headed` flag to see browser actions
- Use `--debug` flag to step through tests
- Check browser console for JavaScript errors
- Verify API responses in network tab

## Contributing

When adding new template publishing tests:

1. Follow the existing page object model pattern
2. Use descriptive test names that explain the workflow
3. Include proper assertions for each verification point
4. Add appropriate timeouts for async operations
5. Update this README if adding new test categories
6. Ensure tests are independent and can run in parallel
