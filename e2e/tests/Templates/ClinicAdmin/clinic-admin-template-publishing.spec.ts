import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { CreateTemplatePage } from '../../../pages/Templates/create-template.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';
import { ProductsPage } from '../../../pages/Products/products.page';

test.describe('Clinic Admin Template Publishing Workflow', () => {
  let allTemplatesPage: AllTemplatesPage;
  let createTemplatePage: CreateTemplatePage;
  let templateDetailsPage: TemplateDetailsPage;
  let productsPage: ProductsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    createTemplatePage = new CreateTemplatePage(page);
    templateDetailsPage = new TemplateDetailsPage(page);
    productsPage = new ProductsPage(page);

    // Login as clinic admin before each test (optimized for reliability)
    await loginAs(page, Role.ClinicAdmin, baseURL);
  });

  test('should allow clinic admin to create a template and publish it as a product', async () => {
    const templateName = `E2E Clinic Admin Template ${Date.now()}`;
    const templateDescription = 'This is a test template created by clinic admin for e2e testing';

    // Step 1: Navigate to templates page
    await allTemplatesPage.goto();

    // Step 2: Create a new template
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(templateName, templateDescription, 'Clinical Assessment');

    // Add clinic-specific questions
    await createTemplatePage.addQuestion('Patient ID or Medical Record Number?', 'TEXT');
    await createTemplatePage.addQuestion('Do you have insurance coverage?', 'BOOLEAN');
    await createTemplatePage.addQuestion('Rate your pain level (1-10)', 'SCALE');
    await createTemplatePage.addQuestion('List current medications', 'TEXT');

    // Save the template
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 3: Verify template appears in templates list
    await allTemplatesPage.waitForPageLoad();
    const templateExists = await allTemplatesPage.verifyTemplateExists(templateName);
    expect(templateExists).toBe(true);

    // Step 4: Verify template is initially in draft state
    const isDraft = await allTemplatesPage.verifyTemplateDraft(templateName);
    expect(isDraft).toBe(true);

    // Step 5: Navigate to template details
    await allTemplatesPage.clickTemplateByTitle(templateName);

    // Step 6: Verify template details page loads
    await templateDetailsPage.waitForPageLoad();
    const detailsTitle = await templateDetailsPage.getTemplateTitle();
    expect(detailsTitle).toContain(templateName);

    // Step 7: Verify template is not published initially
    const isPublished = await templateDetailsPage.isPublished();
    expect(isPublished).toBe(false);

    // Step 8: Publish the template
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 9: Verify template is now published
    const isNowPublished = await templateDetailsPage.isPublished();
    expect(isNowPublished).toBe(true);

    // Step 10: Navigate to products page
    await productsPage.goto();

    // Step 11: Verify the published template appears as a product
    const productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(true);

    // Step 12: Verify product details
    const productDetails = await productsPage.getProductDetails(templateName);
    expect(productDetails.title).toContain(templateName);
  });

  test('should allow clinic admin to manage clinic-specific templates', async () => {
    const clinicTemplateName = `E2E Clinic Specific Template ${Date.now()}`;
    const templateDescription = 'Clinic-specific assessment template';

    // Step 1: Create a clinic-specific template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(clinicTemplateName, templateDescription, 'Clinical Assessment');
    
    // Add clinic workflow specific questions
    await createTemplatePage.addQuestion('Referring physician name', 'TEXT');
    await createTemplatePage.addQuestion('Appointment type (routine/urgent)', 'TEXT');
    await createTemplatePage.addQuestion('Patient consent for treatment?', 'BOOLEAN');
    await createTemplatePage.addQuestion('Emergency contact information', 'TEXT');
    
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 2: Publish the clinic template
    await allTemplatesPage.clickTemplateByTitle(clinicTemplateName);
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 3: Verify clinic template appears as product
    await productsPage.goto();
    const clinicProductExists = await productsPage.verifyPublishedTemplateAsProduct(clinicTemplateName);
    expect(clinicProductExists).toBe(true);

    // Step 4: Verify clinic admin can manage their own templates
    await allTemplatesPage.goto();
    const canSeeTemplate = await allTemplatesPage.verifyTemplateExists(clinicTemplateName);
    expect(canSeeTemplate).toBe(true);
  });

  test('should allow clinic admin to create patient intake templates', async () => {
    const intakeTemplateName = `E2E Patient Intake Template ${Date.now()}`;
    const templateDescription = 'Comprehensive patient intake assessment';

    // Step 1: Create patient intake template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(intakeTemplateName, templateDescription, 'Patient Intake');
    
    // Add comprehensive intake questions
    await createTemplatePage.addQuestion('Patient full name', 'TEXT');
    await createTemplatePage.addQuestion('Date of birth', 'TEXT');
    await createTemplatePage.addQuestion('Primary care physician', 'TEXT');
    await createTemplatePage.addQuestion('Do you have any known allergies?', 'BOOLEAN');
    await createTemplatePage.addQuestion('Rate your current overall health', 'SCALE');
    await createTemplatePage.addQuestion('List any chronic conditions', 'TEXT');
    
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 2: Verify template has appropriate number of questions
    await allTemplatesPage.clickTemplateByTitle(intakeTemplateName);
    const questionsCount = await templateDetailsPage.getQuestionsCount();
    expect(questionsCount).toBeGreaterThanOrEqual(6);

    // Step 3: Publish the intake template
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 4: Verify intake template appears as product
    await productsPage.goto();
    const intakeProductExists = await productsPage.verifyPublishedTemplateAsProduct(intakeTemplateName);
    expect(intakeProductExists).toBe(true);
  });

  test('should allow clinic admin to unpublish and republish templates', async () => {
    const templateName = `E2E Clinic Admin Republish Template ${Date.now()}`;
    const templateDescription = 'Template for testing republish workflow';

    // Step 1: Create, publish, then unpublish a template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(templateName, templateDescription, 'Clinical Assessment');
    await createTemplatePage.addQuestion('Test question for republish', 'TEXT');
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Publish the template
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Verify it appears as product
    await productsPage.goto();
    let productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(true);

    // Unpublish the template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.unpublishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Verify product disappears
    await productsPage.goto();
    productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(false);

    // Step 2: Republish the template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 3: Verify product reappears
    await productsPage.goto();
    productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(true);
  });

  test('should verify clinic admin has same publishing permissions as admin', async () => {
    const templateName = `E2E Clinic Admin Permissions Test ${Date.now()}`;

    // Step 1: Create a template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(templateName, 'Testing clinic admin permissions', 'General');
    await createTemplatePage.addQuestion('Permission test question', 'TEXT');
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 2: Verify clinic admin can access template details
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.waitForPageLoad();
    
    // Step 3: Verify clinic admin can publish templates
    const canPublish = await templateDetailsPage.isPublished();
    expect(typeof canPublish).toBe('boolean'); // Should be able to check publish status
    
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();
    
    const isPublished = await templateDetailsPage.isPublished();
    expect(isPublished).toBe(true);

    // Step 4: Verify published template appears in products
    await productsPage.goto();
    const productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(true);
  });
});
