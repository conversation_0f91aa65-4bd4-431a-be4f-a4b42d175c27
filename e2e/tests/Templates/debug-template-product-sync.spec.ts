import { test, expect } from '@playwright/test';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../pages/Templates/all-templates.page';
import { ProductsPage } from '../../pages/Products/products.page';

test.describe('Debug Template-Product Sync', () => {
  let allTemplatesPage: AllTemplatesPage;
  let productsPage: ProductsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    allTemplatesPage = new AllTemplatesPage(page);
    productsPage = new ProductsPage(page);
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should verify Respiratory Health Questionnaire sync issue', async ({ page }) => {
    // Step 1: Check if Respiratory Health Questionnaire exists and is published
    await allTemplatesPage.goto();
    await allTemplatesPage.waitForPageLoad();
    
    const respiratoryTemplateExists = await allTemplatesPage.verifyTemplateExists('Respiratory Health Questionnaire');
    console.log('Respiratory Health Questionnaire exists:', respiratoryTemplateExists);
    
    if (respiratoryTemplateExists) {
      const isPublished = await allTemplatesPage.verifyTemplatePublished('Respiratory Health Questionnaire');
      console.log('Respiratory Health Questionnaire is published:', isPublished);
    }

    // Step 2: Check products page
    await productsPage.goto();
    await productsPage.waitForPageLoad();
    
    const respiratoryProductExists = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
    console.log('Respiratory Health Questionnaire exists as product:', respiratoryProductExists);

    // Step 3: If template is published but product doesn't exist, try the sync button
    if (respiratoryTemplateExists && !respiratoryProductExists) {
      console.log('Template is published but product does not exist - checking for sync button');
      
      // Look for the sync button
      const syncButton = page.getByRole('button', { name: 'Sync Templates' });
      const syncButtonVisible = await syncButton.isVisible();
      console.log('Sync Templates button visible:', syncButtonVisible);
      
      if (syncButtonVisible) {
        console.log('Clicking Sync Templates button...');
        await syncButton.click();
        
        // Wait for sync to complete
        await page.waitForTimeout(5000);
        
        // Check if products now appear
        await page.reload();
        await productsPage.waitForPageLoad();
        
        const respiratoryProductExistsAfterSync = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
        console.log('Respiratory Health Questionnaire exists as product after sync:', respiratoryProductExistsAfterSync);
      }
    }

    // Step 4: List all published templates
    await allTemplatesPage.goto();
    const publishedCount = await allTemplatesPage.getPublishedTemplatesCount();
    console.log('Total published templates:', publishedCount);

    // Step 5: List all products
    await productsPage.goto();
    const productCount = await productsPage.getProductCount();
    console.log('Total products:', productCount);

    // This test is for debugging, so we don't assert anything
    // Just log the information to understand the sync issue
  });

  test('should manually trigger template-product sync', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await productsPage.waitForPageLoad();

    // Check if sync button is available
    const syncButton = page.getByRole('button', { name: 'Sync Templates' });
    
    try {
      await expect(syncButton).toBeVisible({ timeout: 5000 });
      console.log('Sync Templates button found - clicking it...');
      
      await syncButton.click();
      
      // Wait for sync to complete (look for success message or button state change)
      await page.waitForTimeout(10000);
      
      // Reload page to see results
      await page.reload();
      await productsPage.waitForPageLoad();
      
      const productCount = await productsPage.getProductCount();
      console.log('Product count after sync:', productCount);
      
    } catch (error) {
      console.log('Sync Templates button not found - products might already be synced');
      
      const productCount = await productsPage.getProductCount();
      console.log('Current product count:', productCount);
    }
  });
});
