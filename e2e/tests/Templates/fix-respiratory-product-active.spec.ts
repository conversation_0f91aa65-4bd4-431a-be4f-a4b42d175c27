import { test, expect } from '@playwright/test';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';
import { ProductsPage } from '../../pages/Products/products.page';

test.describe('Fix Respiratory Health Product Active Status', () => {
  let productsPage: ProductsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    productsPage = new ProductsPage(page);
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should fix Respiratory Health product isActive status', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await productsPage.waitForPageLoad();

    console.log('🔧 Fixing Respiratory Health product isActive status...');

    // Execute fix in browser console
    const result = await page.evaluate(async () => {
      try {
        // Import Firebase functions
        const { getFirestore, collection, query, where, getDocs, doc, updateDoc } = await import('firebase/firestore');
        
        const db = getFirestore();
        const productsCollection = collection(db, 'products');
        
        // Find the Respiratory Health product
        const q = query(productsCollection, where('name', '==', 'Respiratory Health Questionnaire'));
        const querySnapshot = await getDocs(q);
        
        if (querySnapshot.empty) {
          return { success: false, error: 'Respiratory Health product not found' };
        }
        
        const productDoc = querySnapshot.docs[0];
        const productData = productDoc.data();
        
        console.log('Found Respiratory Health product:', productData);
        console.log('Current isActive status:', productData.isActive);
        
        // Update the product to set isActive: true
        const productRef = doc(db, 'products', productDoc.id);
        await updateDoc(productRef, {
          isActive: true,
          updatedAt: new Date()
        });
        
        console.log('Updated Respiratory Health product to isActive: true');
        
        return { 
          success: true, 
          productId: productDoc.id,
          previousIsActive: productData.isActive,
          newIsActive: true
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    console.log('Fix result:', result);
    
    if (result.success) {
      console.log('✅ Successfully updated Respiratory Health product isActive status');
      console.log(`Product ID: ${result.productId}`);
      console.log(`Previous isActive: ${result.previousIsActive}`);
      console.log(`New isActive: ${result.newIsActive}`);
      
      // Wait a moment for the change to propagate
      await page.waitForTimeout(2000);
      
      // Reload the products page
      await page.reload();
      await productsPage.waitForPageLoad();
      
      // Check if Respiratory Health Questionnaire now appears
      const respiratoryProductExists = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
      console.log('Respiratory Health Questionnaire exists as product after fix:', respiratoryProductExists);
      
      expect(respiratoryProductExists).toBe(true);
      console.log('🎉 SUCCESS: Respiratory Health Questionnaire is now visible as a product!');
      
    } else {
      console.log('❌ Failed to fix product:', result.error);
      throw new Error(`Failed to fix Respiratory Health product: ${result.error}`);
    }
  });

  test('should verify all published templates now have active products', async ({ page }) => {
    // Navigate to products page
    await productsPage.goto();
    await productsPage.waitForPageLoad();

    // Get final product count
    const productCount = await productsPage.getProductCount();
    console.log('Final product count:', productCount);

    // Verify Respiratory Health Questionnaire is now visible
    const respiratoryProductExists = await productsPage.verifyProductExists('Respiratory Health Questionnaire');
    expect(respiratoryProductExists).toBe(true);
    console.log('✅ Respiratory Health Questionnaire is now available as a product');

    // Expected products based on our earlier findings:
    // 1. E2E Admin Template
    // 2. Sleep Apnea Screener  
    // 3. COPD Assessment
    // 4. Asthma Control
    // 5. COVID-19 Symptom Tracker
    // 6. Respiratory Health Questionnaire (now fixed)
    expect(productCount).toBeGreaterThanOrEqual(6);
    console.log('✅ All published templates now have corresponding products');
  });
});
