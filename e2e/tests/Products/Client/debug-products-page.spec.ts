import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';

test.describe('Debug Products Page', () => {
  test('should debug what is on the products page', async ({ page }) => {
    const baseURL = 'http://localhost:3001';
    
    console.log('Starting products page debug test...');

    // Login as Client
    await loginAs(page, Role.Client, baseURL);

    // Navigate to products page
    console.log('Navigating to products page...');
    await page.goto('/trq/products');
    
    // Wait a bit for the page to load
    await page.waitForTimeout(3000);
    
    // Take a screenshot
    await page.screenshot({ path: 'debug-products-page.png', fullPage: true });
    
    // Get the page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Get the current URL
    const url = page.url();
    console.log('Current URL:', url);
    
    // Check if there are any error messages
    const errorText = await page.locator('text=error').count();
    const errorTextCap = await page.locator('text=Error').count();
    const notFoundText = await page.locator('text=404').count();
    const notFoundTextCap = await page.locator('text=Not Found').count();
    const errorMessages = errorText + errorTextCap + notFoundText + notFoundTextCap;
    console.log('Error messages found:', errorMessages);
    
    // Check for loading indicators
    const loadingIndicators = await page.locator('.MuiCircularProgress-root').count();
    const loadingText = await page.locator('text=Loading').count();
    console.log('Loading indicators found:', loadingIndicators + loadingText);

    // Check for any cards
    const cards = await page.locator('.MuiCard-root').count();
    console.log('Cards found:', cards);

    // Check for any grids
    const grids = await page.locator('.MuiGrid-container').count();
    console.log('Grid containers found:', grids);

    // Check for any text content
    const bodyText = await page.locator('body').textContent();
    console.log('Body text (first 500 chars):', bodyText?.substring(0, 500));

    // Check for specific product-related text
    const productText = await page.locator('text=product').count();
    const productTextCap = await page.locator('text=Product').count();
    const questionnaireText = await page.locator('text=questionnaire').count();
    const questionnaireTextCap = await page.locator('text=Questionnaire').count();
    console.log('Product-related text found:', productText + productTextCap + questionnaireText + questionnaireTextCap);
    
    // Check for navigation elements
    const navElements = await page.locator('nav, .MuiAppBar-root, header').count();
    console.log('Navigation elements found:', navElements);
    
    // Check if we're redirected somewhere else
    if (!url.includes('/products')) {
      console.log('WARNING: We were redirected away from products page!');
    }
    
    console.log('✅ Products page debug test completed!');
  });

  test('should debug product clicking behavior', async ({ page }) => {
    const baseURL = 'http://localhost:3001';

    console.log('Starting product click debug test...');

    // Login as Client
    await loginAs(page, Role.Client, baseURL);

    // Navigate to products page
    console.log('Navigating to products page...');
    await page.goto('/trq/products');

    // Wait a bit for the page to load
    await page.waitForTimeout(3000);

    // Look for the Respiratory Health Questionnaire product
    const productCard = page.locator('.MuiCard-root').filter({ hasText: 'Respiratory Health Questionnaire' }).first();
    const cardCount = await productCard.count();
    console.log('Found Respiratory Health Questionnaire cards:', cardCount);

    if (cardCount > 0) {
      console.log('Product card found, checking for Details button...');

      // Check for Details button
      const detailsButton = productCard.locator('button:has-text("Details"), a:has-text("Details")');
      const detailsButtonCount = await detailsButton.count();
      console.log('Details buttons found:', detailsButtonCount);

      // Take a screenshot before clicking
      await page.screenshot({ path: 'before-product-click.png', fullPage: true });

      // Try to click the Details button or card
      if (detailsButtonCount > 0) {
        console.log('Clicking Details button...');
        await detailsButton.click();
      } else {
        console.log('No Details button found, clicking card...');
        await productCard.click();
      }

      // Wait a bit and see what happens
      await page.waitForTimeout(3000);

      // Take a screenshot after clicking
      await page.screenshot({ path: 'after-product-click.png', fullPage: true });

      // Check current URL
      const currentUrl = page.url();
      console.log('URL after click:', currentUrl);

      // Check page content
      const bodyText = await page.locator('body').textContent();
      console.log('Page content after click (first 300 chars):', bodyText?.substring(0, 300));

      // Look for price elements
      const priceElements = await page.locator('text=/\\$[0-9]+/').count();
      console.log('Price elements found:', priceElements);

      if (priceElements > 0) {
        const priceText = await page.locator('text=/\\$[0-9]+/').first().textContent();
        console.log('First price found:', priceText);
      }

      // Look for Add to Cart buttons
      const addToCartButtons = await page.locator('button:has-text("Add to Cart")').count();
      console.log('Add to Cart buttons found:', addToCartButtons);

      // Look for any buttons
      const allButtons = await page.locator('button').count();
      console.log('Total buttons found:', allButtons);

      // Get all button texts
      const buttonTexts = await page.locator('button').allTextContents();
      console.log('Button texts:', buttonTexts.slice(0, 10)); // First 10 buttons
    } else {
      console.log('No Respiratory Health Questionnaire product found');
    }

    console.log('✅ Product click debug test completed!');
  });
});
