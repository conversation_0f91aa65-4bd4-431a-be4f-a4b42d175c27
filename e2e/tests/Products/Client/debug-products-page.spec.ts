import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';

test.describe('Debug Products Page', () => {
  test('should debug what is on the products page', async ({ page }) => {
    const baseURL = 'http://localhost:3001';
    
    console.log('Starting products page debug test...');

    // Login as Client
    await loginAs(page, Role.Client, baseURL);

    // Navigate to products page
    console.log('Navigating to products page...');
    await page.goto('/trq/products');
    
    // Wait a bit for the page to load
    await page.waitForTimeout(3000);
    
    // Take a screenshot
    await page.screenshot({ path: 'debug-products-page.png', fullPage: true });
    
    // Get the page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Get the current URL
    const url = page.url();
    console.log('Current URL:', url);
    
    // Check if there are any error messages
    const errorMessages = await page.locator('text=error, text=Error, text=404, text=Not Found').count();
    console.log('Error messages found:', errorMessages);
    
    // Check for loading indicators
    const loadingIndicators = await page.locator('.MuiCircularProgress-root, text=Loading').count();
    console.log('Loading indicators found:', loadingIndicators);
    
    // Check for any cards
    const cards = await page.locator('.MuiCard-root').count();
    console.log('Cards found:', cards);
    
    // Check for any grids
    const grids = await page.locator('.MuiGrid-container').count();
    console.log('Grid containers found:', grids);
    
    // Check for any text content
    const bodyText = await page.locator('body').textContent();
    console.log('Body text (first 500 chars):', bodyText?.substring(0, 500));
    
    // Check for specific product-related text
    const productText = await page.locator('text=product, text=Product, text=questionnaire, text=Questionnaire').count();
    console.log('Product-related text found:', productText);
    
    // Check for navigation elements
    const navElements = await page.locator('nav, .MuiAppBar-root, header').count();
    console.log('Navigation elements found:', navElements);
    
    // Check if we're redirected somewhere else
    if (!url.includes('/products')) {
      console.log('WARNING: We were redirected away from products page!');
    }
    
    console.log('✅ Products page debug test completed!');
  });
});
