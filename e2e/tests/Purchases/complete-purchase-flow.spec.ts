import { test, expect } from '@playwright/test';
import { ProductsPage } from '../../pages/Products/products.page';
import { ProductDetailsPage } from '../../pages/Products/productDetails.page';
import { CartPage } from '../../pages/Products/cart.page';
import { CartCheckoutPage } from '../../pages/Products/cartCheckout.page';
import { MyPurchasesPage } from '../../pages/Purchases/myPurchases.page';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/RBAC/[types]/Role';

test.describe('Complete Purchase Flow', () => {
  let productsPage: ProductsPage;
  let productDetailsPage: ProductDetailsPage;
  let cartPage: CartPage;
  let cartCheckoutPage: CartCheckoutPage;
  let myPurchasesPage: MyPurchasesPage;
  let baseURL: string;

  // Test data
  const testProduct = {
    name: 'Respiratory Health Questionnaire',
    searchTerm: 'respiratory',
    expectedPrice: '$25.00' // Adjust based on your actual product pricing
  };

  test.beforeEach(async ({ page, baseURL: testBaseURL }) => {
    baseURL = testBaseURL || 'http://localhost:3001';
    
    // Initialize page objects
    productsPage = new ProductsPage(page);
    productDetailsPage = new ProductDetailsPage(page);
    cartPage = new CartPage(page);
    cartCheckoutPage = new CartCheckoutPage(page);
    myPurchasesPage = new MyPurchasesPage(page);

    console.log('Test setup complete - ready for complete purchase flow testing');
  });

  test('should complete full purchase flow from product selection to purchase confirmation', async ({ page }) => {
    console.log('Starting complete purchase flow test...');

    // Step 1: Login as Patient
    console.log('Step 1: Login as Patient');
    await loginAs(page, Role.Patient, baseURL);

    // Step 2: Navigate to products and find a product
    console.log('Step 2: Browse products');
    await productsPage.goto();
    await productsPage.verifyProductsDisplayed();

    // Take screenshot for debugging
    await page.screenshot({ path: 'test-results/01-products-page.png' });

    // Search for a specific product
    await productsPage.searchProducts(testProduct.searchTerm);
    
    // Click on the product to view details
    const productFound = await productsPage.clickProductByTitle(testProduct.name);
    if (!productFound) {
      console.log('Product not found, clicking first available product');
      await productsPage.clickFirstProduct();
    }

    // Step 3: Add product to cart
    console.log('Step 3: Add product to cart');
    await productDetailsPage.waitForPageLoad();
    await page.screenshot({ path: 'test-results/02-product-details.png' });
    
    await productDetailsPage.addToCart();
    
    // Verify product was added to cart
    await cartPage.goto();
    await cartPage.waitForPageLoad();
    await page.screenshot({ path: 'test-results/03-cart-with-product.png' });
    
    const itemCount = await cartPage.getItemCount();
    expect(itemCount).toBeGreaterThan(0);
    console.log(`✅ Product added to cart. Items in cart: ${itemCount}`);

    // Step 4: Proceed to checkout
    console.log('Step 4: Proceed to checkout');
    await cartPage.proceedToCheckout();
    
    // Wait for cart checkout page to load
    await cartCheckoutPage.waitForPageLoad();
    await page.screenshot({ path: 'test-results/04-checkout-start.png' });

    // Step 5: Complete the checkout process
    console.log('Step 5: Complete checkout process');
    const checkoutResult = await cartCheckoutPage.completeCheckoutFlow();
    
    await page.screenshot({ path: 'test-results/05-checkout-complete.png' });
    
    expect(checkoutResult.orderId).toBeTruthy();
    expect(checkoutResult.transactionId).toBeTruthy();
    console.log(`✅ Order completed. Order ID: ${checkoutResult.orderId}, Transaction ID: ${checkoutResult.transactionId}`);

    // Step 6: Navigate to purchases to verify the purchase appears
    console.log('Step 6: Verify purchase in My Purchases');
    await cartCheckoutPage.viewMyOrders();
    
    // Wait for purchases page to load and refresh data
    await myPurchasesPage.waitForPageLoad();
    await myPurchasesPage.waitForDataRefresh();
    await page.screenshot({ path: 'test-results/06-my-purchases.png' });

    // Verify the purchase appears in the list
    const purchaseCount = await myPurchasesPage.getPurchaseRowsCount();
    expect(purchaseCount).toBeGreaterThan(0);
    console.log(`✅ Found ${purchaseCount} purchases in My Purchases`);

    // Verify the most recent purchase
    const recentPurchase = await myPurchasesPage.getMostRecentPurchase();
    expect(recentPurchase).toBeTruthy();
    expect(recentPurchase?.status.toLowerCase()).toContain('completed');
    console.log(`✅ Most recent purchase status: ${recentPurchase?.status}, Amount: ${recentPurchase?.amount}`);

    // Verify summary statistics updated
    const totalPurchases = await myPurchasesPage.getTotalPurchasesCount();
    const completedPurchases = await myPurchasesPage.getCompletedPurchasesCount();
    expect(totalPurchases).toBeGreaterThan(0);
    expect(completedPurchases).toBeGreaterThan(0);
    console.log(`✅ Purchase statistics - Total: ${totalPurchases}, Completed: ${completedPurchases}`);

    console.log('✅ Complete purchase flow test passed!');
  });

  test('should handle cart operations before checkout', async ({ page }) => {
    console.log('Starting cart operations test...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Navigate to products and add multiple items
    await productsPage.goto();
    await productsPage.verifyProductsDisplayed();

    // Add first product
    await productsPage.clickFirstProduct();
    await productDetailsPage.waitForPageLoad();
    await productDetailsPage.addToCart();

    // Go to cart and verify
    await cartPage.goto();
    await cartPage.waitForPageLoad();
    
    const initialCount = await cartPage.getItemCount();
    expect(initialCount).toBeGreaterThan(0);
    console.log(`✅ Cart has ${initialCount} items`);

    // Test cart functionality
    if (initialCount > 0) {
      const itemTitles = await cartPage.getItemTitles();
      console.log(`Cart items: ${itemTitles.join(', ')}`);
      
      // Verify we can proceed to checkout
      await cartPage.proceedToCheckout();
      await cartCheckoutPage.waitForPageLoad();
      
      const currentStep = await cartCheckoutPage.getCurrentStep();
      expect(currentStep).toBe(0); // Should start at step 0 (Review Order)
      console.log(`✅ Checkout started at step: ${currentStep}`);
    }

    console.log('✅ Cart operations test passed!');
  });

  test('should handle empty cart scenario', async ({ page }) => {
    console.log('Starting empty cart test...');

    // Login as Patient
    await loginAs(page, Role.Patient, baseURL);

    // Navigate directly to cart (assuming it's empty)
    await cartPage.goto();
    await cartPage.waitForPageLoad();

    // Check if cart is empty
    const isEmpty = await cartPage.isCartEmpty();
    const itemCount = await cartPage.getItemCount();
    
    if (isEmpty || itemCount === 0) {
      console.log('✅ Cart is empty as expected');
      
      // Verify we can navigate to products
      await cartPage.continueShopping();
      await productsPage.verifyProductsDisplayed();
      console.log('✅ Successfully navigated to products from empty cart');
    } else {
      console.log(`Cart has ${itemCount} items - clearing cart for next test`);
    }

    console.log('✅ Empty cart test completed!');
  });

  test('should navigate between checkout steps properly', async ({ page }) => {
    console.log('Starting checkout navigation test...');

    // Login and add a product to cart
    await loginAs(page, Role.Patient, baseURL);
    await productsPage.goto();
    await productsPage.clickFirstProduct();
    await productDetailsPage.addToCart();
    await cartPage.goto();
    await cartPage.proceedToCheckout();
    await cartCheckoutPage.waitForPageLoad();

    // Test step navigation
    let currentStep = await cartCheckoutPage.getCurrentStep();
    expect(currentStep).toBe(0);
    console.log(`Started at step: ${currentStep}`);

    // Navigate to billing step
    await cartCheckoutPage.clickNext();
    currentStep = await cartCheckoutPage.getCurrentStep();
    expect(currentStep).toBe(1);
    console.log(`Advanced to step: ${currentStep}`);

    // Navigate back to review
    await cartCheckoutPage.clickBack();
    currentStep = await cartCheckoutPage.getCurrentStep();
    expect(currentStep).toBe(0);
    console.log(`Went back to step: ${currentStep}`);

    console.log('✅ Checkout navigation test passed!');
  });

  test('should show purchase in My Purchases after checkout', async ({ page }) => {
    console.log('Starting purchase verification test...');

    // Get initial purchase count
    await loginAs(page, Role.Patient, baseURL);
    await myPurchasesPage.goto();
    const initialCount = await myPurchasesPage.getTotalPurchasesCount();
    console.log(`Initial purchase count: ${initialCount}`);

    // Make a purchase
    await productsPage.goto();
    await productsPage.clickFirstProduct();
    await productDetailsPage.addToCart();
    await cartPage.goto();
    await cartPage.proceedToCheckout();
    await cartCheckoutPage.completeCheckoutFlow();

    // Verify purchase appears
    await cartCheckoutPage.viewMyOrders();
    await myPurchasesPage.waitForDataRefresh();
    
    const finalCount = await myPurchasesPage.getTotalPurchasesCount();
    expect(finalCount).toBeGreaterThan(initialCount);
    console.log(`Final purchase count: ${finalCount}`);

    // Verify purchase details
    const recentPurchase = await myPurchasesPage.getMostRecentPurchase();
    expect(recentPurchase).toBeTruthy();
    expect(recentPurchase?.status.toLowerCase()).toContain('completed');

    console.log('✅ Purchase verification test passed!');
  });
});