import { Page, Locator, expect } from '@playwright/test';

export class TemplateSelectionDialog {
  readonly page: Page; // Dialog exists within a page
  readonly dialog: Locator;
  readonly createButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.dialog = page.locator('[data-testid="template-selection-dialog"]');
    this.createButton = this.dialog.locator('[data-testid="dialog-create-questionnaire-button"]');
  }

  async waitForDialog() {
    await expect(this.dialog).toBeVisible();
  }

  getTemplateCard(templateId: string): Locator {
    return this.dialog.locator(`[data-testid="template-card-${templateId}"]`);
  }

  async selectTemplate(templateId: string) {
    const card = this.getTemplateCard(templateId);
    await expect(card).toBeVisible({ timeout: 10000 });
    await card.click();
    // Verify selection by checking for the CheckIcon within the card
    await expect(card.locator('svg[data-testid="CheckIcon"]')).toBeVisible({ timeout: 5000 });
    // Wait for selection to register
    await this.page.waitForTimeout(500);
  }

  async submit() {
    await expect(this.createButton).toBeVisible();
    await expect(this.createButton).toBeEnabled();
    await this.createButton.click();
    await expect(this.dialog).not.toBeVisible({ timeout: 10000 });
  }

  async confirm() {
    await this.submit();
  }
}
