import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../utils/basePage';

/**
 * Products page object representing the products listing page
 */
export class ProductsPage extends BasePage {
  readonly productCards: Locator;
  readonly productTitles: Locator;
  readonly productPrices: Locator;
  readonly productImages: Locator;
  readonly filterDropdown: Locator;
  readonly sortDropdown: Locator;
  readonly searchInput: Locator;
  readonly searchButton: Locator;
  readonly categoryFilters: Locator;

  /**
   * Initialize the Products page object
   * @param page Playwright page
   */
  constructor(page: Page) {
    super(page);
    // Use actual selectors from ProductCard component
    this.productCards = page.locator('.MuiCard-root, [role="button"]').filter({ hasText: '$' });
    this.productTitles = page.locator('.MuiCard-root h6, .MuiTypography-h6');
    this.productPrices = page.locator('.MuiCard-root .MuiTypography-h6:has-text("$")');
    this.productImages = page.locator('.MuiCard-root .MuiCardMedia-root img');
    this.filterDropdown = page.locator('button:has-text("Filter"), [aria-label*="filter"]');
    this.sortDropdown = page.locator('button:has-text("Sort"), [aria-label*="sort"]');
    this.searchInput = page.locator('input[placeholder*="Search products"]').first();
    this.searchButton = page.locator('button:has-text("Search"), [aria-label*="search"]');
    this.categoryFilters = page.locator('.MuiChip-root, button:has-text("Category")');
  }

  /**
   * Navigate to the products page
   */
  async goto() {
    await this.page.goto('/trq/products');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the products page to load
   */
  async waitForPageLoad() {
    // Wait for either product cards to load or a "no products" message
    await Promise.race([
      this.page.waitForSelector('.MuiCard-root', { state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('text=No products found', { state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('.MuiGrid-container', { state: 'visible', timeout: 15000 })
    ]);
    // Remove networkidle wait as it causes timeouts with the sync service
    await this.page.waitForTimeout(2000);
  }

  /**
   * Get the number of products displayed on the page
   * @returns The number of products
   */
  async getProductCount(): Promise<number> {
    return await this.productCards.count();
  }

  /**
   * Click on a product by its index
   * @param index The index of the product to click (0-based)
   */
  async clickProduct(index: number) {
    await this.productCards.nth(index).click();
  }

  /**
   * Click on a product by its title
   * @param title The title of the product to click
   */
  async clickProductByTitle(title: string) {
    // Look for the product title and click on the card or details button
    const productCard = this.page.locator('.MuiCard-root').filter({ hasText: title }).first();
    await productCard.waitFor({ state: 'visible', timeout: 10000 });

    // Try to click the "Details" button first, otherwise click the card
    const detailsButton = productCard.locator('button:has-text("Details"), a:has-text("Details")');
    if (await detailsButton.count() > 0) {
      await detailsButton.click();
    } else {
      await productCard.click();
    }
  }

  /**
   * Search for products
   * @param searchTerm The search term
   */
  async searchProducts(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.searchButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Filter products by category
   * @param category The category to filter by
   */
  async filterByCategory(category: string) {
    await this.page.locator(`[data-testid="category-filter"]:has-text("${category}")`).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Sort products
   * @param sortOption The sort option (e.g., 'Price: Low to High')
   */
  async sortProducts(sortOption: string) {
    await this.sortDropdown.click();
    await this.page.locator(`[data-testid="sort-option"]:has-text("${sortOption}")`).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get all product titles
   * @returns Array of product titles
   */
  async getProductTitles(): Promise<string[]> {
    const count = await this.productTitles.count();
    const titles: string[] = [];

    for (let i = 0; i < count; i++) {
      titles.push((await this.productTitles.nth(i).textContent()) || '');
    }

    return titles;
  }

  /**
   * Get all product prices
   * @returns Array of product prices
   */
  async getProductPrices(): Promise<string[]> {
    const count = await this.productPrices.count();
    const prices: string[] = [];

    for (let i = 0; i < count; i++) {
      prices.push((await this.productPrices.nth(i).textContent()) || '');
    }

    return prices;
  }

  /**
   * Verify that products are displayed
   */
  async verifyProductsDisplayed() {
    // Wait for page to load first
    await this.waitForPageLoad();

    // Check if we have products or if there's a "no products" message
    const hasProducts = await this.productCards.count() > 0;
    const hasNoProductsMessage = await this.page.locator('text=No products found').count() > 0;

    // Either we should have products OR a "no products" message (both are valid states)
    expect(hasProducts || hasNoProductsMessage).toBeTruthy();

    if (hasProducts) {
      const count = await this.getProductCount();
      expect(count).toBeGreaterThan(0);
    }
  }

  /**
   * Verify that a specific product exists by title
   * @param title The title of the product to verify
   * @returns True if the product exists, false otherwise
   */
  async verifyProductExists(title: string): Promise<boolean> {
    await this.waitForPageLoad();
    try {
      const productCard = this.page.locator('.MuiCard-root').filter({ hasText: title }).first();
      await expect(productCard).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get a product card by title
   * @param title The title of the product
   * @returns The product card locator
   */
  async getProductByTitle(title: string): Promise<Locator> {
    return this.page.locator('.MuiCard-root').filter({ hasText: title }).first();
  }

  /**
   * Verify that a product from a published template appears in the products list
   * @param templateTitle The title of the template that was published
   * @returns True if the product appears, false otherwise
   */
  async verifyPublishedTemplateAsProduct(templateTitle: string): Promise<boolean> {
    await this.waitForPageLoad();

    // Search for the product by template title
    if (await this.searchInput.isVisible()) {
      await this.searchProducts(templateTitle);
    }

    // Check if a product with the template title exists
    return await this.verifyProductExists(templateTitle);
  }

  /**
   * Get product details by title
   * @param title The title of the product
   * @returns Object containing product details
   */
  async getProductDetails(title: string): Promise<{
    title: string;
    price: string;
    description: string;
  }> {
    const productCard = await this.getProductByTitle(title);
    await expect(productCard).toBeVisible();

    const titleElement = productCard.locator('h6, .MuiTypography-h6').first();
    const priceElement = productCard.locator('.MuiTypography-h6:has-text("$")').first();
    const descriptionElement = productCard.locator('.MuiCardContent-root p, .MuiTypography-body2').first();

    return {
      title: (await titleElement.textContent()) || '',
      price: (await priceElement.textContent()) || '',
      description: (await descriptionElement.textContent()) || ''
    };
  }

  /**
   * Wait for a specific product to appear (useful after publishing a template)
   * @param title The title of the product to wait for
   * @param timeout Maximum time to wait in milliseconds
   */
  async waitForProductToAppear(title: string, timeout: number = 30000) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      await this.page.reload();
      await this.waitForPageLoad();

      if (await this.verifyProductExists(title)) {
        return true;
      }

      await this.page.waitForTimeout(2000);
    }

    throw new Error(`Product "${title}" did not appear within ${timeout}ms`);
  }
}
