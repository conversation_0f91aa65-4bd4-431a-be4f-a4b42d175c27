import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../utils/basePage';

/**
 * Cart Checkout page object representing the new cart checkout flow
 */
export class CartCheckoutPage extends BasePage {
  // Step navigation
  readonly stepper: Locator;
  readonly nextButton: Locator;
  readonly backButton: Locator;
  
  // Review step
  readonly orderItems: Locator;
  readonly orderSubtotal: Locator;
  readonly orderTax: Locator;
  readonly orderTotal: Locator;
  
  // Billing step
  readonly billingFirstNameInput: Locator;
  readonly billingLastNameInput: Locator;
  readonly billingEmailInput: Locator;
  readonly billingPhoneInput: Locator;
  readonly billingAddressInput: Locator;
  readonly billingCityInput: Locator;
  readonly billingPostalCodeInput: Locator;
  
  // Payment step
  readonly paymentMethodRadios: Locator;
  readonly creditCardNumberInput: Locator;
  readonly creditCardNameInput: Locator;
  readonly creditCardExpiryInput: Locator;
  readonly creditCardCvvInput: Locator;
  readonly completeOrderButton: Locator;
  readonly processingIndicator: Locator;
  
  // Confirmation step
  readonly orderCompleteMessage: Locator;
  readonly orderIdDisplay: Locator;
  readonly transactionIdDisplay: Locator;
  readonly viewMyOrdersButton: Locator;
  readonly continueShoppingButton: Locator;

  constructor(page: Page) {
    super(page);
    
    // Step navigation
    this.stepper = page.locator('.MuiStepper-root');
    this.nextButton = page.locator('button').filter({ hasText: /Continue to|Next/ });
    this.backButton = page.locator('button').filter({ hasText: 'Back' });
    
    // Review step
    this.orderItems = page.locator('[data-testid="order-item"]');
    this.orderSubtotal = page.locator('text=Subtotal').locator('..').locator('typography').last();
    this.orderTax = page.locator('text=Tax').locator('..').locator('typography').last();
    this.orderTotal = page.locator('text=Total').locator('..').locator('typography').last();
    
    // Billing step
    this.billingFirstNameInput = page.locator('input[label*="First Name"], input').filter({ hasText: /First Name/ }).first();
    this.billingLastNameInput = page.locator('input[label*="Last Name"], input').filter({ hasText: /Last Name/ }).first();
    this.billingEmailInput = page.locator('input[type="email"]');
    this.billingPhoneInput = page.locator('input[label*="Phone"], input').filter({ hasText: /Phone/ }).first();
    this.billingAddressInput = page.locator('input[label*="Address"], input').filter({ hasText: /Address/ }).first();
    this.billingCityInput = page.locator('input[label*="City"], input').filter({ hasText: /City/ }).first();
    this.billingPostalCodeInput = page.locator('input[label*="Postal Code"], input').filter({ hasText: /Postal/ }).first();
    
    // Payment step
    this.paymentMethodRadios = page.locator('input[type="radio"]');
    this.creditCardNumberInput = page.locator('input').filter({ hasText: /Card Number/ }).first();
    this.creditCardNameInput = page.locator('input').filter({ hasText: /Name on Card/ }).first();
    this.creditCardExpiryInput = page.locator('input').filter({ hasText: /Expiry/ }).first();
    this.creditCardCvvInput = page.locator('input').filter({ hasText: /CVV/ }).first();
    this.completeOrderButton = page.locator('button').filter({ hasText: /Complete Order/ });
    this.processingIndicator = page.locator('text=Processing Payment');
    
    // Confirmation step
    this.orderCompleteMessage = page.locator('text=Order Complete');
    this.orderIdDisplay = page.locator('text=Order ID').locator('..');
    this.transactionIdDisplay = page.locator('text=Transaction ID').locator('..');
    this.viewMyOrdersButton = page.locator('button').filter({ hasText: /View My Orders/ });
    this.continueShoppingButton = page.locator('button').filter({ hasText: /Continue Shopping/ });
  }

  /**
   * Navigate to the cart checkout page
   */
  async goto() {
    await this.page.goto('/trq/purchases/cart/checkout');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the checkout page to load
   */
  async waitForPageLoad() {
    await this.page.waitForSelector('.MuiStepper-root', { state: 'visible' });
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get the current step number
   */
  async getCurrentStep(): Promise<number> {
    const activeStep = await this.page.locator('.MuiStep-root.Mui-active').count();
    return activeStep - 1; // 0-based index
  }

  /**
   * Proceed to the next step
   */
  async clickNext() {
    await this.nextButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Go back to the previous step
   */
  async clickBack() {
    await this.backButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Fill billing information
   */
  async fillBillingInfo(info: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    postalCode: string;
  }) {
    await this.billingFirstNameInput.fill(info.firstName);
    await this.billingLastNameInput.fill(info.lastName);
    await this.billingEmailInput.fill(info.email);
    await this.billingPhoneInput.fill(info.phone);
    await this.billingAddressInput.fill(info.address);
    await this.billingCityInput.fill(info.city);
    await this.billingPostalCodeInput.fill(info.postalCode);
  }

  /**
   * Select payment method
   */
  async selectPaymentMethod(method: 'credit' | 'paypal') {
    const radioButton = this.page.locator(`input[type="radio"][value="${method}"]`);
    await radioButton.click();
  }

  /**
   * Fill credit card information
   */
  async fillCreditCardInfo(cardInfo: {
    number: string;
    name: string;
    expiry: string;
    cvv: string;
  }) {
    await this.creditCardNumberInput.fill(cardInfo.number);
    await this.creditCardNameInput.fill(cardInfo.name);
    await this.creditCardExpiryInput.fill(cardInfo.expiry);
    await this.creditCardCvvInput.fill(cardInfo.cvv);
  }

  /**
   * Complete the order
   */
  async completeOrder() {
    await this.completeOrderButton.click();
    
    // Wait for either processing to complete or error
    await Promise.race([
      this.page.waitForSelector('text=Order Complete', { timeout: 30000 }),
      this.page.waitForSelector('text=Payment failed', { timeout: 30000 })
    ]);
  }

  /**
   * Verify order completion
   */
  async verifyOrderComplete(): Promise<boolean> {
    return await this.orderCompleteMessage.isVisible();
  }

  /**
   * Get order ID from confirmation
   */
  async getOrderId(): Promise<string> {
    const orderIdText = await this.orderIdDisplay.textContent();
    return orderIdText?.replace(/Order ID:\s*/, '') || '';
  }

  /**
   * Get transaction ID from confirmation
   */
  async getTransactionId(): Promise<string> {
    const transactionIdText = await this.transactionIdDisplay.textContent();
    return transactionIdText?.replace(/Transaction ID:\s*/, '') || '';
  }

  /**
   * Navigate to My Orders
   */
  async viewMyOrders() {
    await this.viewMyOrdersButton.click();
    await this.page.waitForURL('**/purchases/my-purchases');
  }

  /**
   * Continue shopping
   */
  async continueShopping() {
    await this.continueShoppingButton.click();
    await this.page.waitForURL('**/products');
  }

  /**
   * Complete the entire checkout flow with test data
   */
  async completeCheckoutFlow(): Promise<{ orderId: string; transactionId: string }> {
    // Step 1: Review Order - just proceed
    await this.clickNext();

    // Step 2: Fill Billing Information
    await this.fillBillingInfo({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '************',
      address: '123 Test St',
      city: 'Test City',
      postalCode: '12345'
    });
    await this.clickNext();

    // Step 3: Payment
    await this.selectPaymentMethod('credit');
    await this.fillCreditCardInfo({
      number: '****************',
      name: 'Test User',
      expiry: '12/25',
      cvv: '123'
    });
    await this.completeOrder();

    // Step 4: Get confirmation details
    const orderId = await this.getOrderId();
    const transactionId = await this.getTransactionId();

    return { orderId, transactionId };
  }
}