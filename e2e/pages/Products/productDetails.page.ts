import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../utils/basePage';

/**
 * Product Details page object representing the product details page
 */
export class ProductDetailsPage extends BasePage {
  readonly productTitle: Locator;
  readonly productPrice: Locator;
  readonly productDescription: Locator;
  readonly productImage: Locator;
  readonly addToCartButton: Locator;
  readonly quantityInput: Locator;
  readonly increaseQuantityButton: Locator;
  readonly decreaseQuantityButton: Locator;
  readonly backToProductsButton: Locator;
  readonly productCategory: Locator;
  readonly productRating: Locator;
  readonly productReviews: Locator;

  /**
   * Initialize the Product Details page object
   * @param page Playwright page
   */
  constructor(page: Page) {
    super(page);
    this.productTitle = page.locator('[data-testid="product-detail-title"]').or(page.locator('h1, h2, h3').first());
    this.productPrice = page.locator('[data-testid="product-detail-price"]').or(page.locator('text=/\\$[0-9]+/').first());
    this.productDescription = page.locator('[data-testid="product-detail-description"]').or(page.locator('p').first());
    this.productImage = page.locator('[data-testid="product-detail-image"]').or(page.locator('img').first());
    this.addToCartButton = page.locator('[data-testid="add-to-cart-button"]').or(page.locator('button:has-text("Add to Cart")'));
    this.quantityInput = page.locator('[data-testid="quantity-input"]').or(page.locator('input[type="number"]'));
    this.increaseQuantityButton = page.locator('[data-testid="increase-quantity-button"]').or(page.locator('button:has-text("+")'));
    this.decreaseQuantityButton = page.locator('[data-testid="decrease-quantity-button"]').or(page.locator('button:has-text("-")'));
    this.backToProductsButton = page.locator('[data-testid="back-to-products-button"]').or(page.locator('button:has-text("Back")'));
    this.productCategory = page.locator('[data-testid="product-detail-category"]').or(page.locator('.category, .MuiChip-root').first());
    this.productRating = page.locator('[data-testid="product-detail-rating"]').or(page.locator('.rating, .MuiRating-root').first());
    this.productReviews = page.locator('[data-testid="product-reviews"]').or(page.locator('.reviews').first());
  }

  /**
   * Navigate to a specific product details page
   * @param productId The ID of the product
   */
  async goto(productId: string) {
    await this.page.goto(`/trq/products/${productId}/details`);
    await this.waitForPageLoad();
  }

  /**
   * Wait for the product details page to load
   */
  async waitForPageLoad() {
    try {
      await this.page.waitForSelector('[data-testid="product-detail-title"]', { state: 'visible', timeout: 5000 });
    } catch (error) {
      // Fallback to more generic selectors - look for the tab structure we saw in debug
      console.log('Product detail title not found with data-testid, trying fallback selectors...');
      try {
        await this.page.waitForSelector('text=Description', { state: 'visible', timeout: 10000 });
        console.log('Found Description tab, product details page loaded');
      } catch (fallbackError) {
        await this.page.waitForSelector('h1, h2, h3, .MuiTypography-h1, .MuiTypography-h2, .MuiTypography-h3', { state: 'visible', timeout: 10000 });
      }
    }
  }

  /**
   * Get the product title
   * @returns The product title
   */
  async getProductTitle(): Promise<string> {
    return (await this.productTitle.textContent()) || '';
  }

  /**
   * Get the product price
   * @returns The product price
   */
  async getProductPrice(): Promise<string> {
    return (await this.productPrice.textContent()) || '';
  }

  /**
   * Get the product description
   * @returns The product description
   */
  async getProductDescription(): Promise<string> {
    return (await this.productDescription.textContent()) || '';
  }

  /**
   * Set the quantity of the product
   * @param quantity The quantity to set
   */
  async setQuantity(quantity: number) {
    await this.quantityInput.fill(quantity.toString());
  }

  /**
   * Increase the quantity by clicking the increase button
   * @param times Number of times to increase (default: 1)
   */
  async increaseQuantity(times: number = 1) {
    for (let i = 0; i < times; i++) {
      await this.increaseQuantityButton.click();
    }
  }

  /**
   * Decrease the quantity by clicking the decrease button
   * @param times Number of times to decrease (default: 1)
   */
  async decreaseQuantity(times: number = 1) {
    for (let i = 0; i < times; i++) {
      await this.decreaseQuantityButton.click();
    }
  }

  /**
   * Get the current quantity
   * @returns The current quantity
   */
  async getQuantity(): Promise<number> {
    const value = await this.quantityInput.inputValue();
    return parseInt(value, 10);
  }

  /**
   * Add the product to the cart
   */
  async addToCart() {
    await this.addToCartButton.click();
    // Wait for the cart update animation or notification
    await this.page.waitForTimeout(500);
  }

  /**
   * Navigate back to the products listing page
   */
  async backToProducts() {
    await this.backToProductsButton.click();
    await this.page.waitForSelector('[data-testid="product-card"]', { state: 'visible' });
  }

  /**
   * Verify that the product details are displayed correctly
   * @param expectedTitle Expected product title
   * @param expectedPrice Expected product price
   */
  async verifyProductDetails(expectedTitle: string, expectedPrice: string) {
    const title = await this.getProductTitle();
    const price = await this.getProductPrice();

    expect(title).toBe(expectedTitle);
    expect(price).toContain(expectedPrice);
    expect(await this.productImage.isVisible()).toBeTruthy();
    expect(await this.addToCartButton.isVisible()).toBeTruthy();
  }
}
