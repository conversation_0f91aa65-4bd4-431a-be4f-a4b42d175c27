import { Page, Locator, expect } from '@playwright/test';

export class TemplateDetailsPage {
  readonly page: Page;
  readonly backToTemplatesButton: Locator;
  readonly templateTitle: Locator;
  readonly templateDescription: Locator;
  readonly publishSwitch: Locator;
  readonly editButton: Locator;
  readonly createAndAssignButton: Locator;
  readonly publishConfirmDialog: Locator;
  readonly publishConfirmButton: Locator;
  readonly publishCancelButton: Locator;
  readonly publishSuccessAlert: Locator;
  readonly publishErrorAlert: Locator;
  readonly loadingSpinner: Locator;
  readonly questionsSection: Locator;
  readonly questionCards: Locator;

  constructor(page: Page) {
    this.page = page;
    this.backToTemplatesButton = page.locator('button:has-text("Back to Templates")');
    this.templateTitle = page.locator('[data-testid="template-title"], h4, h5, h6').first();
    this.templateDescription = page.locator('[data-testid="template-description"]');
    this.publishSwitch = page.locator('input[aria-label="Publish Template Toggle"]');
    this.editButton = page.locator('button[aria-label="Edit Template"], button:has([data-testid="EditIcon"])');
    this.createAndAssignButton = page.locator('button:has-text("Create & Assign to Patient")');
    this.publishConfirmDialog = page.locator('[role="dialog"]:has-text("Publish Template"), [role="dialog"]:has-text("Unpublish Template")');
    this.publishConfirmButton = page.locator('[role="dialog"] button:has-text("Publish"), [role="dialog"] button:has-text("Unpublish")');
    this.publishCancelButton = page.locator('[role="dialog"] button:has-text("Cancel")');
    this.publishSuccessAlert = page.locator('.MuiAlert-root:has-text("successfully")');
    this.publishErrorAlert = page.locator('.MuiAlert-root:has-text("error"), .MuiAlert-root:has-text("failed")');
    this.loadingSpinner = page.locator('.MuiCircularProgress-root');
    this.questionsSection = page.locator('[data-testid="questions-section"]');
    this.questionCards = page.locator('[data-testid="question-card"]');
  }

  async goto(templateId: string) {
    await this.page.goto(`/trq/templates/${templateId}/details`);
    await expect(this.page).toHaveURL(`/trq/templates/${templateId}/details`);
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    // Wait for the template details to load
    await Promise.race([
      this.templateTitle.waitFor({ state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('text=Template not found', { state: 'visible', timeout: 15000 })
    ]);
    await this.page.waitForLoadState('networkidle');
  }

  async getTemplateTitle(): Promise<string> {
    await expect(this.templateTitle).toBeVisible();
    return await this.templateTitle.textContent() || '';
  }

  async getTemplateDescription(): Promise<string> {
    try {
      await expect(this.templateDescription).toBeVisible({ timeout: 5000 });
      return await this.templateDescription.textContent() || '';
    } catch {
      return '';
    }
  }

  async isPublished(): Promise<boolean> {
    await expect(this.publishSwitch).toBeVisible();
    return await this.publishSwitch.isChecked();
  }

  async publishTemplate() {
    const isCurrentlyPublished = await this.isPublished();
    
    if (isCurrentlyPublished) {
      throw new Error('Template is already published');
    }

    // Click the publish switch
    await expect(this.publishSwitch).toBeVisible();
    await this.publishSwitch.click();

    // Wait for confirmation dialog
    await expect(this.publishConfirmDialog).toBeVisible();
    
    // Confirm the publish action
    await expect(this.publishConfirmButton).toBeVisible();
    await this.publishConfirmButton.click();

    // Wait for dialog to close
    await expect(this.publishConfirmDialog).not.toBeVisible({ timeout: 10000 });
    
    // Wait for any loading to complete
    await this.page.waitForTimeout(2000);
  }

  async unpublishTemplate() {
    const isCurrentlyPublished = await this.isPublished();
    
    if (!isCurrentlyPublished) {
      throw new Error('Template is not currently published');
    }

    // Click the publish switch to unpublish
    await expect(this.publishSwitch).toBeVisible();
    await this.publishSwitch.click();

    // Wait for confirmation dialog
    await expect(this.publishConfirmDialog).toBeVisible();
    
    // Confirm the unpublish action
    await expect(this.publishConfirmButton).toBeVisible();
    await this.publishConfirmButton.click();

    // Wait for dialog to close
    await expect(this.publishConfirmDialog).not.toBeVisible({ timeout: 10000 });
    
    // Wait for any loading to complete
    await this.page.waitForTimeout(2000);
  }

  async cancelPublishAction() {
    await expect(this.publishConfirmDialog).toBeVisible();
    await expect(this.publishCancelButton).toBeVisible();
    await this.publishCancelButton.click();
    await expect(this.publishConfirmDialog).not.toBeVisible({ timeout: 5000 });
  }

  async clickEdit() {
    await expect(this.editButton).toBeVisible();
    await this.editButton.click();
  }

  async clickCreateAndAssign() {
    await expect(this.createAndAssignButton).toBeVisible();
    await this.createAndAssignButton.click();
  }

  async clickBackToTemplates() {
    await expect(this.backToTemplatesButton).toBeVisible();
    await this.backToTemplatesButton.click();
    await expect(this.page).toHaveURL('/trq/templates');
  }

  async verifyPublishSuccess() {
    await expect(this.publishSuccessAlert).toBeVisible({ timeout: 10000 });
  }

  async verifyPublishError() {
    await expect(this.publishErrorAlert).toBeVisible({ timeout: 10000 });
  }

  async waitForPublishingComplete() {
    // Wait for any loading spinner to disappear
    try {
      await expect(this.loadingSpinner).toBeVisible({ timeout: 2000 });
      await expect(this.loadingSpinner).not.toBeVisible({ timeout: 15000 });
    } catch {
      // No loading spinner found, continue
    }
    
    // Additional wait for any async operations
    await this.page.waitForTimeout(1000);
  }

  async getQuestionsCount(): Promise<number> {
    try {
      await expect(this.questionCards).toBeVisible({ timeout: 5000 });
      return await this.questionCards.count();
    } catch {
      return 0;
    }
  }

  async verifyTemplateCanBeEdited(): Promise<boolean> {
    try {
      await expect(this.editButton).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}
