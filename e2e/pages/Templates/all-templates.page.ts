import { Page, Locator, expect } from '@playwright/test';

export class AllTemplatesPage {
  readonly page: Page;
  readonly createTemplateButton: Locator;
  readonly searchInput: Locator;
  readonly categoryFilter: Locator;
  readonly templateCards: Locator;
  readonly templateTitles: Locator;
  readonly editButtons: Locator;
  readonly deleteButtons: Locator;
  readonly publishedChips: Locator;
  readonly draftChips: Locator;

  constructor(page: Page) {
    this.page = page;
    this.createTemplateButton = page.locator('button:has-text("Create Template")');
    this.searchInput = page.locator('textbox:has-text("Search templates")');
    this.categoryFilter = page.locator('combobox:has-text("Category")');
    this.templateCards = page.locator('button[aria-label*="View details for template"]');
    this.templateTitles = page.locator('h6'); // Template titles are in h6 elements
    this.editButtons = page.locator('button:has-text("Edit")');
    this.deleteButtons = page.locator('button:has-text("Delete")');
    this.publishedChips = page.locator('text=Published');
    this.draftChips = page.locator('text=Draft');
  }

  async goto() {
    await this.page.goto('/trq/templates');
    await expect(this.page).toHaveURL('/trq/templates');
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    // Wait for either template cards to load or empty state
    await Promise.race([
      this.page.waitForSelector('button:has-text("Create Template")', { state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('text=No templates found', { state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('main', { state: 'visible', timeout: 15000 })
    ]);
    // Remove networkidle wait as it's causing timeouts
    await this.page.waitForTimeout(1000);
  }

  async clickCreateTemplate() {
    await expect(this.createTemplateButton).toBeVisible();
    await this.createTemplateButton.click();
    await expect(this.page).toHaveURL('/trq/templates/add');
  }

  async searchTemplates(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.waitForTimeout(1000); // Wait for search to process
  }

  async selectCategoryFilter(category: string) {
    await this.categoryFilter.click();
    await this.page.locator(`text="${category}"`).click();
    await this.page.waitForTimeout(1000); // Wait for filter to apply
  }

  async getTemplateCount(): Promise<number> {
    return await this.templateCards.count();
  }

  async getTemplateByTitle(title: string): Promise<Locator> {
    return this.page.getByRole('button', { name: `View details for template ${title}` });
  }

  async clickTemplateByTitle(title: string) {
    const templateCard = await this.getTemplateByTitle(title);
    await expect(templateCard).toBeVisible();
    await templateCard.click();
  }

  async editTemplateByTitle(title: string) {
    const templateCard = await this.getTemplateByTitle(title);
    await expect(templateCard).toBeVisible();
    
    // Click the edit button within the template card
    const editButton = templateCard.locator('button[aria-label="Edit"], button:has([data-testid="EditIcon"])');
    await expect(editButton).toBeVisible();
    await editButton.click();
  }

  async deleteTemplateByTitle(title: string) {
    const templateCard = await this.getTemplateByTitle(title);
    await expect(templateCard).toBeVisible();
    
    // Click the delete button within the template card
    const deleteButton = templateCard.locator('button[aria-label="Delete"], button:has([data-testid="DeleteIcon"])');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();
  }

  async verifyTemplateExists(title: string): Promise<boolean> {
    try {
      const templateCard = await this.getTemplateByTitle(title);
      await expect(templateCard).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  async verifyTemplatePublished(title: string): Promise<boolean> {
    const templateCard = await this.getTemplateByTitle(title);
    try {
      await expect(templateCard).toBeVisible({ timeout: 5000 });
      // Check if the template card contains "Published" text
      const cardText = await templateCard.textContent();
      return cardText?.includes('Published') || false;
    } catch {
      return false;
    }
  }

  async verifyTemplateDraft(title: string): Promise<boolean> {
    const templateCard = await this.getTemplateByTitle(title);
    try {
      await expect(templateCard).toBeVisible({ timeout: 5000 });
      // Check if the template card contains "Draft" text
      const cardText = await templateCard.textContent();
      return cardText?.includes('Draft') || false;
    } catch {
      return false;
    }
  }

  async getPublishedTemplatesCount(): Promise<number> {
    return await this.publishedChips.count();
  }

  async getDraftTemplatesCount(): Promise<number> {
    return await this.draftChips.count();
  }
}
