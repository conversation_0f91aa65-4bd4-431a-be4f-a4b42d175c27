import { Page, Locator, expect } from '@playwright/test';

export class CreateTemplatePage {
  readonly page: Page;
  readonly nameInput: Locator;
  readonly descriptionInput: Locator;
  readonly categorySelect: Locator;
  readonly saveButton: Locator;
  readonly addQuestionButton: Locator;
  readonly questionCards: Locator;
  readonly successAlert: Locator;
  readonly errorAlert: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.nameInput = page.getByRole('textbox', { name: 'Template Name' });
    this.descriptionInput = page.getByRole('textbox', { name: 'Description' });
    this.categorySelect = page.getByRole('combobox').first(); // The category combobox
    this.saveButton = page.getByRole('button', { name: 'Create Template' });
    this.addQuestionButton = page.getByRole('button', { name: 'Add Question' });
    this.questionCards = page.locator('.MuiPaper-root').filter({ has: page.locator('input[label*="Question"]') });
    this.successAlert = page.locator('.MuiAlert-root:has-text("success"), .MuiAlert-root:has-text("created")');
    this.errorAlert = page.locator('.MuiAlert-root:has-text("error"), .MuiAlert-root:has-text("failed")');
    this.loadingSpinner = page.locator('.MuiCircularProgress-root');
  }

  async goto() {
    await this.page.goto('/trq/templates/add');
    await expect(this.page).toHaveURL('/trq/templates/add');
    await this.waitForPageLoad();
  }

  async gotoEdit(templateId: string) {
    await this.page.goto(`/trq/templates/${templateId}/edit`);
    await expect(this.page).toHaveURL(`/trq/templates/${templateId}/edit`);
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    await expect(this.nameInput).toBeVisible({ timeout: 15000 });
    await expect(this.addQuestionButton).toBeVisible({ timeout: 15000 });
    // Remove networkidle wait as it can cause timeouts
    await this.page.waitForTimeout(1000);
  }

  async fillBasicDetails(name: string, description: string, category?: string) {
    await expect(this.nameInput).toBeVisible();
    await this.nameInput.fill(name);

    await expect(this.descriptionInput).toBeVisible();
    await this.descriptionInput.fill(description);

    if (category) {
      await this.selectCategory(category);
    }

    // Fill in the estimated time field (might be required)
    const estimatedTimeInput = this.page.getByRole('spinbutton', { name: 'Estimated Time (minutes)' });
    await estimatedTimeInput.fill('10');

    // Wait for form validation
    await this.page.waitForTimeout(1000);
  }

  async selectCategory(category: string) {
    await expect(this.categorySelect).toBeVisible();
    await this.categorySelect.click();
    // Wait for dropdown to open
    await this.page.waitForTimeout(500);
    await this.page.getByRole('option', { name: category }).click();
  }

  async addQuestion(questionText: string, questionType: string = 'Text') {
    await expect(this.addQuestionButton).toBeVisible();
    await this.addQuestionButton.click();

    // Wait for the new question form to appear
    await this.page.waitForTimeout(2000);

    // Find the question input that was just added
    // From the snapshot, I can see it's "textbox Question 1"
    const questionInput = this.page.getByRole('textbox', { name: 'Question 1' });

    await expect(questionInput).toBeVisible();
    await questionInput.fill(questionText);

    // Select question type if needed
    if (questionType !== 'Text') {
      // Find the question type combobox for this question
      // From the snapshot, I can see there are multiple comboboxes
      // The category is first, then question type comboboxes
      const questionTypeCombobox = this.page.getByRole('combobox', { name: 'Text' });

      await questionTypeCombobox.click();
      await this.page.waitForTimeout(500);
      await this.page.getByRole('option', { name: questionType }).click();
    }

    await this.page.waitForTimeout(500);
  }

  async save() {
    await expect(this.saveButton).toBeVisible();
    await expect(this.saveButton).toBeEnabled();

    // Check for any validation errors before submitting
    const validationErrors = await this.page.locator('text=required, text=error, .error').allTextContents();
    if (validationErrors.length > 0) {
      console.log('Validation errors before submit:', validationErrors);
    }

    // Try clicking the button and wait for some response
    await this.saveButton.click();

    // Wait for either navigation or loading state
    await this.page.waitForTimeout(3000);

    // Check if we're still on the same page
    const currentUrl = this.page.url();
    console.log('URL after save button click:', currentUrl);

    // Check for any error messages that appeared after clicking
    const postClickErrors = await this.page.locator('.MuiAlert-root, .error, [role="alert"]').allTextContents();
    if (postClickErrors.length > 0) {
      console.log('Errors after clicking save:', postClickErrors);
    }
  }

  async verifySuccess() {
    // Wait for either success alert or navigation to templates page
    await Promise.race([
      expect(this.successAlert).toBeVisible({ timeout: 15000 }),
      expect(this.page).toHaveURL('/trq/templates', { timeout: 15000 })
    ]);

    // Additional wait for any async operations
    await this.page.waitForTimeout(2000);

    // Check if we're on the templates page (success)
    const currentUrl = this.page.url();
    if (!currentUrl.includes('/trq/templates')) {
      // If not on templates page, wait a bit more and check again
      await this.page.waitForTimeout(3000);
      await expect(this.page).toHaveURL('/trq/templates', { timeout: 10000 });
    }
  }

  async verifyError() {
    await expect(this.errorAlert).toBeVisible({ timeout: 10000 });
  }

  async waitForSaveComplete() {
    // Wait for any loading spinner to disappear
    try {
      await expect(this.loadingSpinner).toBeVisible({ timeout: 2000 });
      await expect(this.loadingSpinner).not.toBeVisible({ timeout: 15000 });
    } catch {
      // No loading spinner found, continue
    }
    
    // Additional wait for any async operations
    await this.page.waitForTimeout(1000);
  }

  async getQuestionsCount(): Promise<number> {
    try {
      return await this.questionCards.count();
    } catch {
      return 0;
    }
  }

  async deleteQuestion(index: number) {
    const questionCards = await this.questionCards.all();
    if (index >= questionCards.length) {
      throw new Error(`Question index ${index} is out of bounds`);
    }
    
    const questionCard = questionCards[index];
    const deleteButton = questionCard.locator('button[aria-label="Delete"], button:has([data-testid="DeleteIcon"])');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();
    
    // Confirm deletion if there's a confirmation dialog
    try {
      const confirmButton = this.page.locator('[role="dialog"] button:has-text("Delete"), [role="dialog"] button:has-text("Confirm")');
      await expect(confirmButton).toBeVisible({ timeout: 2000 });
      await confirmButton.click();
    } catch {
      // No confirmation dialog, continue
    }
    
    await this.page.waitForTimeout(500);
  }

  async moveQuestionUp(index: number) {
    const questionCards = await this.questionCards.all();
    if (index >= questionCards.length || index === 0) {
      throw new Error(`Cannot move question at index ${index} up`);
    }
    
    const questionCard = questionCards[index];
    const moveUpButton = questionCard.locator('button[aria-label="Move Up"], button:has([data-testid="ArrowUpwardIcon"])');
    await expect(moveUpButton).toBeVisible();
    await moveUpButton.click();
    await this.page.waitForTimeout(500);
  }

  async moveQuestionDown(index: number) {
    const questionCards = await this.questionCards.all();
    if (index >= questionCards.length - 1) {
      throw new Error(`Cannot move question at index ${index} down`);
    }
    
    const questionCard = questionCards[index];
    const moveDownButton = questionCard.locator('button[aria-label="Move Down"], button:has([data-testid="ArrowDownwardIcon"])');
    await expect(moveDownButton).toBeVisible();
    await moveDownButton.click();
    await this.page.waitForTimeout(500);
  }
}
