import { Page, Locator, expect } from '@playwright/test';

export class ClientDashboardPage {
  readonly page: Page;
  readonly welcomeSection: Locator;
  readonly totalPatientsCard: Locator;
  readonly activePatientsCard: Locator;
  readonly totalPurchasesCard: Locator;
  readonly totalQuestionnairesCard: Locator;
  readonly purchasesTab: Locator;
  readonly patientsTab: Locator;
  readonly recentPurchasesSection: Locator;
  readonly browseAvailablePlansButton: Locator;
  readonly manageProfileButton: Locator;
  readonly myPurchasesButton: Locator;
  readonly myQuestionnairesButton: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Main sections
    this.welcomeSection = page.locator('text=Welcome back').locator('..');
    
    // Stats cards
    this.totalPatientsCard = page.locator('[data-testid="total-patients-card"]').or(page.locator('text=Total Patients').locator('..'));
    this.activePatientsCard = page.locator('[data-testid="active-patients-card"]').or(page.locator('text=Active Patients').locator('..'));
    this.totalPurchasesCard = page.locator('[data-testid="total-purchases-card"]').or(page.locator('text=Purchases').locator('..'));
    this.totalQuestionnairesCard = page.locator('[data-testid="total-questionnaires-card"]').or(page.locator('text=Questionnaires').locator('..'));
    
    // Navigation tabs
    this.purchasesTab = page.locator('text=Purchases & Billing');
    this.patientsTab = page.locator('text=Patients');
    
    // Purchases section
    this.recentPurchasesSection = page.locator('text=Recent Purchases').locator('..');
    this.browseAvailablePlansButton = page.locator('button:has-text("Browse Available Plans")');
    
    // Navigation buttons
    this.manageProfileButton = page.locator('button:has-text("Manage Profile")');
    this.myPurchasesButton = page.locator('button:has-text("My Purchases"), [href*="/purchases"]');
    this.myQuestionnairesButton = page.locator('button:has-text("My Questionnaires"), [href*="/questionnaires"]');
  }

  async goto() {
    await this.page.goto('/trq/clients/home');
    await this.waitForDashboardToLoad();
  }

  async waitForDashboardToLoad() {
    await expect(this.welcomeSection).toBeVisible({ timeout: 10000 });
    try {
      await this.page.waitForLoadState('networkidle', { timeout: 15000 });
    } catch (error) {
      console.log('Network idle timeout, but continuing...');
      // Continue anyway as the welcome section is visible
    }
  }

  /**
   * Navigate to purchases tab
   */
  async navigateToPurchasesTab() {
    await this.purchasesTab.click();
    await this.page.waitForTimeout(1000);
  }

  /**
   * Navigate to patients tab
   */
  async navigateToPatientsTab() {
    await this.patientsTab.click();
    await this.page.waitForTimeout(1000);
  }

  /**
   * Click on the purchases card to navigate to purchases
   */
  async clickPurchasesCard() {
    await this.totalPurchasesCard.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Click on the questionnaires card to navigate to questionnaires
   */
  async clickQuestionnairesCard() {
    await this.totalQuestionnairesCard.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Navigate to my purchases page
   */
  async navigateToMyPurchases() {
    await this.myPurchasesButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Navigate to my questionnaires page
   */
  async navigateToMyQuestionnaires() {
    await this.myQuestionnairesButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Navigate to browse available plans
   */
  async navigateToBrowseAvailablePlans() {
    await this.browseAvailablePlansButton.click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get the total purchases count from the dashboard
   */
  async getTotalPurchasesCount(): Promise<number> {
    const countText = await this.totalPurchasesCard.locator('h3').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Get the total questionnaires count from the dashboard
   */
  async getTotalQuestionnairesCount(): Promise<number> {
    const countText = await this.totalQuestionnairesCard.locator('h3').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Get the total patients count from the dashboard
   */
  async getTotalPatientsCount(): Promise<number> {
    const countText = await this.totalPatientsCard.locator('h3').textContent();
    return parseInt(countText || '0', 10);
  }

  /**
   * Verify that a purchase is reflected in the dashboard stats
   */
  async verifyPurchaseInStats(expectedCount: number) {
    const actualCount = await this.getTotalPurchasesCount();
    expect(actualCount).toBeGreaterThanOrEqual(expectedCount);
  }

  /**
   * Verify that questionnaire assignments are reflected in the dashboard stats
   */
  async verifyQuestionnaireInStats(expectedCount: number) {
    const actualCount = await this.getTotalQuestionnairesCount();
    expect(actualCount).toBeGreaterThanOrEqual(expectedCount);
  }

  /**
   * Wait for dashboard stats to update
   */
  async waitForStatsToUpdate() {
    await this.page.waitForTimeout(2000);
    await this.page.reload();
    await this.waitForDashboardToLoad();
  }
}
