import { Page, Locator, expect } from '@playwright/test';

export class ClientAssignmentDialogPage {
  readonly page: Page;
  readonly dialog: Locator;
  readonly dialogTitle: Locator;
  readonly patientSearchInput: Locator;
  readonly patientCards: Locator;
  readonly submitButton: Locator;
  readonly cancelButton: Locator;
  readonly loadingIndicator: Locator;
  readonly successMessage: Locator;
  readonly errorMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Main dialog
    this.dialog = page.locator('[data-testid="assign-questionnaire-dialog"], [role="dialog"]');
    this.dialogTitle = this.dialog.locator('[data-testid="dialog-title"], .MuiDialogTitle-root');
    
    // Patient selection
    this.patientSearchInput = this.dialog.locator('[data-testid="patient-search-input"], input[placeholder*="Search patients"]');
    this.patientCards = this.dialog.locator('[data-testid="patient-card"], .MuiCard-root');
    
    // Action buttons
    this.submitButton = this.dialog.locator('[data-testid="dialog-submit-button"], button:has-text("Assign")');
    this.cancelButton = this.dialog.locator('[data-testid="dialog-cancel-button"], button:has-text("Cancel")');
    
    // Status indicators
    this.loadingIndicator = this.dialog.locator('.MuiCircularProgress-root');
    this.successMessage = page.locator('[role="alert"]:has-text("success"), text*=successfully assigned');
    this.errorMessage = page.locator('[role="alert"]:has-text("error"), text*=error');
  }

  async waitForDialog() {
    await expect(this.dialog).toBeVisible({ timeout: 10000 });
    await expect(this.dialogTitle).toBeVisible();
  }

  /**
   * Search for a patient by name or email
   */
  async searchForPatient(searchTerm: string) {
    await this.patientSearchInput.fill(searchTerm);
    await this.page.waitForTimeout(1000); // Wait for search results to filter
  }

  /**
   * Get a patient card by name
   */
  getPatientCard(patientName: string): Locator {
    return this.dialog.locator(`.MuiCard-root:has-text("${patientName}")`);
  }

  /**
   * Get a patient radio button by patient ID
   */
  getPatientRadio(patientId: string): Locator {
    return this.dialog.locator(`input[type="radio"][value="${patientId}"]`);
  }

  /**
   * Select a patient by name
   */
  async selectPatientByName(patientName: string) {
    const patientCard = this.getPatientCard(patientName);
    await expect(patientCard).toBeVisible({ timeout: 10000 });
    await patientCard.click();
    
    // Wait for selection to register
    await this.page.waitForTimeout(500);
  }

  /**
   * Select a patient by ID
   */
  async selectPatientById(patientId: string) {
    const patientRadio = this.getPatientRadio(patientId);
    await expect(patientRadio).toBeVisible({ timeout: 10000 });
    await patientRadio.check();
    
    // Wait for selection to register
    await this.page.waitForTimeout(500);
  }

  /**
   * Submit the assignment
   */
  async submitAssignment() {
    await expect(this.submitButton).toBeVisible();
    await expect(this.submitButton).toBeEnabled();
    await this.submitButton.click();
  }

  /**
   * Cancel the assignment
   */
  async cancelAssignment() {
    await this.cancelButton.click();
    await expect(this.dialog).not.toBeVisible({ timeout: 10000 });
  }

  /**
   * Complete the assignment process
   */
  async completeAssignment(patientName: string) {
    await this.waitForDialog();
    await this.selectPatientByName(patientName);
    await this.submitAssignment();
    await this.waitForAssignmentCompletion();
  }

  /**
   * Wait for assignment to complete
   */
  async waitForAssignmentCompletion() {
    // Wait for either success message or dialog to close
    await Promise.race([
      this.successMessage.waitFor({ state: 'visible', timeout: 10000 }),
      this.dialog.waitFor({ state: 'hidden', timeout: 10000 })
    ]);
  }

  /**
   * Verify successful assignment
   */
  async verifySuccessfulAssignment() {
    try {
      await expect(this.successMessage).toBeVisible({ timeout: 5000 });
    } catch {
      // If no success message, check if dialog closed (which also indicates success)
      await expect(this.dialog).not.toBeVisible({ timeout: 5000 });
    }
  }

  /**
   * Verify assignment error
   */
  async verifyAssignmentError(errorText?: string) {
    await expect(this.errorMessage).toBeVisible({ timeout: 10000 });
    if (errorText) {
      await expect(this.errorMessage).toContainText(errorText);
    }
  }

  /**
   * Get all available patient names
   */
  async getAvailablePatientNames(): Promise<string[]> {
    const cards = this.patientCards;
    const count = await cards.count();
    const names: string[] = [];
    
    for (let i = 0; i < count; i++) {
      const cardText = await cards.nth(i).textContent();
      if (cardText) {
        // Extract patient name from card text (assuming it's the first line or prominent text)
        const lines = cardText.split('\n').map(line => line.trim()).filter(line => line);
        if (lines.length > 0) {
          names.push(lines[0]);
        }
      }
    }
    
    return names;
  }

  /**
   * Verify that a specific patient is available for assignment
   */
  async verifyPatientAvailable(patientName: string) {
    const patientCard = this.getPatientCard(patientName);
    await expect(patientCard).toBeVisible({ timeout: 10000 });
  }

  /**
   * Get the number of available patients
   */
  async getAvailablePatientCount(): Promise<number> {
    return await this.patientCards.count();
  }

  /**
   * Verify dialog title contains expected text
   */
  async verifyDialogTitle(expectedText: string) {
    await expect(this.dialogTitle).toContainText(expectedText);
  }

  /**
   * Wait for patient search results to load
   */
  async waitForPatientSearchResults() {
    await this.page.waitForTimeout(1000);
    await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 10000 });
  }
}
