import { Page, Locator, expect } from '@playwright/test';

export class ClientPurchasesPage {
  readonly page: Page;
  readonly purchasesDataGrid: Locator;
  readonly purchaseRows: Locator;
  readonly searchInput: Locator;
  readonly filterDropdown: Locator;
  readonly assignButton: Locator;
  readonly viewDetailsButton: Locator;
  readonly loadingIndicator: Locator;
  readonly noPurchasesMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Main data grid for purchases
    this.purchasesDataGrid = page.locator('[data-testid="purchases-data-grid"], .MuiDataGrid-root');
    this.purchaseRows = this.purchasesDataGrid.locator('.MuiDataGrid-row');
    
    // Search and filter controls
    this.searchInput = page.locator('input[placeholder*="Search"], [data-testid="search-input"]');
    this.filterDropdown = page.locator('[data-testid="filter-dropdown"]');
    
    // Action buttons
    this.assignButton = page.locator('button:has-text("Assign"), [data-testid*="assign"]');
    this.viewDetailsButton = page.locator('button:has-text("View Details"), [data-testid*="view"]');
    
    // Status indicators
    this.loadingIndicator = page.locator('.MuiCircularProgress-root');
    this.noPurchasesMessage = page.locator('text=No purchases found, text=No recent purchases');
  }

  async goto() {
    await this.page.goto('/trq/purchases/my-purchases');
    await this.waitForPageToLoad();
  }

  async waitForPageToLoad() {
    // Wait for either the data grid to load or no purchases message to appear
    try {
      await Promise.race([
        this.purchasesDataGrid.waitFor({ state: 'visible', timeout: 10000 }),
        this.noPurchasesMessage.waitFor({ state: 'visible', timeout: 10000 }),
        this.page.waitForSelector('text=My Purchases', { state: 'visible', timeout: 10000 })
      ]);
    } catch (error) {
      console.log('Page load timeout, but continuing...');
    }

    // Wait for loading to complete with shorter timeout
    try {
      await this.page.waitForLoadState('networkidle', { timeout: 5000 });
    } catch (error) {
      console.log('Network idle timeout, but continuing...');
    }
  }

  /**
   * Get a purchase row by product name
   */
  getPurchaseRowByProduct(productName: string): Locator {
    return this.purchaseRows.filter({ hasText: productName }).first();
  }

  /**
   * Get the assign button for a specific purchase
   */
  getAssignButtonForPurchase(productName: string): Locator {
    const row = this.getPurchaseRowByProduct(productName);
    return row.locator('button:has-text("Assign"), [data-testid*="assign"]');
  }

  /**
   * Click assign button for a specific purchased product
   */
  async clickAssignForProduct(productName: string) {
    const assignButton = this.getAssignButtonForPurchase(productName);
    await expect(assignButton).toBeVisible({ timeout: 10000 });
    await assignButton.click();
  }

  /**
   * Verify that a purchased product exists in the list
   */
  async verifyPurchasedProductExists(productName: string) {
    const row = this.getPurchaseRowByProduct(productName);
    await expect(row).toBeVisible({ timeout: 10000 });
  }

  /**
   * Get the status of a purchased product
   */
  async getPurchaseStatus(productName: string): Promise<string> {
    const row = this.getPurchaseRowByProduct(productName);
    const statusCell = row.locator('.MuiDataGrid-cell[data-field="status"], .MuiDataGrid-cell:has-text("Completed"), .MuiDataGrid-cell:has-text("Pending")');
    return await statusCell.textContent() || '';
  }

  /**
   * Verify purchase status
   */
  async verifyPurchaseStatus(productName: string, expectedStatus: string) {
    const row = this.getPurchaseRowByProduct(productName);
    const statusCell = row.locator('.MuiDataGrid-cell[data-field="status"]');
    await expect(statusCell).toContainText(expectedStatus, { ignoreCase: true });
  }

  /**
   * Search for purchases
   */
  async searchPurchases(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.waitForTimeout(1000); // Wait for search to process
  }

  /**
   * Get the count of purchases in the list
   */
  async getPurchaseCount(): Promise<number> {
    const rows = this.purchaseRows;
    return await rows.count();
  }

  /**
   * Verify that no purchases are displayed
   */
  async verifyNoPurchases() {
    await expect(this.noPurchasesMessage).toBeVisible({ timeout: 10000 });
  }

  /**
   * Wait for purchase data to load
   */
  async waitForPurchaseDataToLoad() {
    // Wait for loading indicator to disappear
    await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 15000 });
    
    // Wait for either purchases to appear or no data message
    await Promise.race([
      this.purchaseRows.first().waitFor({ state: 'visible', timeout: 10000 }),
      this.noPurchasesMessage.waitFor({ state: 'visible', timeout: 10000 })
    ]);
  }

  /**
   * Get all purchase product names from the current page
   */
  async getAllPurchaseProductNames(): Promise<string[]> {
    const productCells = this.purchasesDataGrid.locator('.MuiDataGrid-cell[data-field="productName"], .MuiDataGrid-cell[data-field="name"]');
    const count = await productCells.count();
    const names: string[] = [];
    
    for (let i = 0; i < count; i++) {
      const name = await productCells.nth(i).textContent();
      if (name) {
        names.push(name.trim());
      }
    }
    
    return names;
  }

  /**
   * Verify purchase date
   */
  async verifyPurchaseDate(productName: string, expectedDate?: string) {
    const row = this.getPurchaseRowByProduct(productName);
    const dateCell = row.locator('.MuiDataGrid-cell[data-field="purchaseDate"], .MuiDataGrid-cell[data-field="date"]');
    
    if (expectedDate) {
      await expect(dateCell).toContainText(expectedDate);
    } else {
      // Just verify the date cell exists and has content
      await expect(dateCell).not.toBeEmpty();
    }
  }

  /**
   * Get purchase details for a product
   */
  async getPurchaseDetails(productName: string): Promise<{
    productName: string;
    status: string;
    date: string;
    price: string;
  }> {
    const row = this.getPurchaseRowByProduct(productName);
    
    const status = await row.locator('.MuiDataGrid-cell[data-field="status"]').textContent() || '';
    const date = await row.locator('.MuiDataGrid-cell[data-field="purchaseDate"], .MuiDataGrid-cell[data-field="date"]').textContent() || '';
    const price = await row.locator('.MuiDataGrid-cell[data-field="totalPrice"], .MuiDataGrid-cell[data-field="price"]').textContent() || '';
    
    return {
      productName,
      status: status.trim(),
      date: date.trim(),
      price: price.trim()
    };
  }

  /**
   * Verify that a purchase can be assigned (has assign button enabled)
   */
  async verifyPurchaseCanBeAssigned(productName: string) {
    const assignButton = this.getAssignButtonForPurchase(productName);
    await expect(assignButton).toBeVisible();
    await expect(assignButton).toBeEnabled();
  }
}
