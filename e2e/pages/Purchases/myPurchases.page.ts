import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../utils/basePage';

/**
 * My Purchases page object representing the user's purchase history
 */
export class MyPurchasesPage extends BasePage {
  readonly pageTitle: Locator;
  readonly totalPurchasesCard: Locator;
  readonly totalAmountCard: Locator;
  readonly completedPurchasesCard: Locator;
  readonly pendingPurchasesCard: Locator;
  readonly newPurchaseButton: Locator;
  readonly searchInput: Locator;
  readonly statusFilter: Locator;
  readonly purchasesList: Locator;
  readonly purchaseRows: Locator;
  readonly emptyStateMessage: Locator;
  readonly browseProductsButton: Locator;
  readonly dataGrid: Locator;

  constructor(page: Page) {
    super(page);
    
    this.pageTitle = page.locator('h3').filter({ hasText: /My Purchases|Purchase History/ });
    this.totalPurchasesCard = page.locator('[data-testid="total-purchases-card"]').or(
      page.locator('text=Total Purchases').locator('..').locator('..')
    );
    this.totalAmountCard = page.locator('[data-testid="total-amount-card"]').or(
      page.locator('text=Total Amount').locator('..').locator('..')
    );
    this.completedPurchasesCard = page.locator('[data-testid="completed-purchases-card"]').or(
      page.locator('text=Completed Purchases').locator('..').locator('..')
    );
    this.pendingPurchasesCard = page.locator('[data-testid="pending-purchases-card"]').or(
      page.locator('text=Pending Purchases').locator('..').locator('..')
    );
    this.newPurchaseButton = page.locator('button').filter({ hasText: /New Purchase/ });
    this.searchInput = page.locator('input[placeholder*="Search"]');
    this.statusFilter = page.locator('select, input[role="combobox"]').filter({ hasText: /Status|All Statuses/ }).first();
    this.purchasesList = page.locator('.MuiDataGrid-root');
    this.purchaseRows = page.locator('.MuiDataGrid-row');
    this.emptyStateMessage = page.locator('text=No Purchases Found');
    this.browseProductsButton = page.locator('button').filter({ hasText: /Browse Products/ });
    this.dataGrid = page.locator('.MuiDataGrid-root');
  }

  /**
   * Navigate to the My Purchases page
   */
  async goto() {
    await this.page.goto('/trq/purchases/my-purchases');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the page to load
   */
  async waitForPageLoad() {
    // Wait for either the data grid or empty state to appear
    await Promise.race([
      this.page.waitForSelector('.MuiDataGrid-root', { state: 'visible', timeout: 10000 }).catch(() => {}),
      this.page.waitForSelector('text=No Purchases Found', { state: 'visible', timeout: 10000 }).catch(() => {})
    ]);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get the total number of purchases from the summary card
   */
  async getTotalPurchasesCount(): Promise<number> {
    const cardText = await this.totalPurchasesCard.textContent();
    const match = cardText?.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : 0;
  }

  /**
   * Get the total amount from the summary card
   */
  async getTotalAmount(): Promise<string> {
    const cardText = await this.totalAmountCard.textContent();
    const match = cardText?.match(/\$[\d,]+\.?\d*/);
    return match ? match[0] : '$0.00';
  }

  /**
   * Get the number of completed purchases
   */
  async getCompletedPurchasesCount(): Promise<number> {
    const cardText = await this.completedPurchasesCard.textContent();
    const match = cardText?.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : 0;
  }

  /**
   * Get the number of pending purchases
   */
  async getPendingPurchasesCount(): Promise<number> {
    const cardText = await this.pendingPurchasesCard.textContent();
    const match = cardText?.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : 0;
  }

  /**
   * Get the number of purchase rows in the grid
   */
  async getPurchaseRowsCount(): Promise<number> {
    return await this.purchaseRows.count();
  }

  /**
   * Check if the empty state is shown
   */
  async isEmptyStateShown(): Promise<boolean> {
    return await this.emptyStateMessage.isVisible();
  }

  /**
   * Search for purchases
   */
  async searchPurchases(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Filter purchases by status
   */
  async filterByStatus(status: string) {
    await this.statusFilter.click();
    await this.page.locator(`text=${status}`).click();
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Click on a purchase row by index
   */
  async clickPurchaseRow(index: number) {
    await this.purchaseRows.nth(index).click();
    await this.page.waitForURL('**/purchases/**');
  }

  /**
   * Get purchase data from a specific row
   */
  async getPurchaseRowData(index: number): Promise<{
    date: string;
    quantity: string;
    amount: string;
    status: string;
  }> {
    const row = this.purchaseRows.nth(index);
    const cells = row.locator('.MuiDataGrid-cell');
    
    return {
      date: (await cells.nth(0).textContent()) || '',
      quantity: (await cells.nth(1).textContent()) || '',
      amount: (await cells.nth(2).textContent()) || '',
      status: (await cells.nth(3).textContent()) || ''
    };
  }

  /**
   * Verify that a purchase with specific details exists
   */
  async verifyPurchaseExists(expectedPurchase: {
    minimumAmount?: string;
    status?: string;
    containsDate?: string;
  }): Promise<boolean> {
    const rowCount = await this.getPurchaseRowsCount();
    
    for (let i = 0; i < rowCount; i++) {
      const rowData = await this.getPurchaseRowData(i);
      
      let matches = true;
      
      if (expectedPurchase.status) {
        matches = matches && rowData.status.toLowerCase().includes(expectedPurchase.status.toLowerCase());
      }
      
      if (expectedPurchase.containsDate) {
        matches = matches && rowData.date.includes(expectedPurchase.containsDate);
      }
      
      if (expectedPurchase.minimumAmount) {
        const amountText = rowData.amount.replace(/[^0-9.]/g, '');
        const amount = parseFloat(amountText);
        const minAmount = parseFloat(expectedPurchase.minimumAmount.replace(/[^0-9.]/g, ''));
        matches = matches && amount >= minAmount;
      }
      
      if (matches) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Click the New Purchase button
   */
  async clickNewPurchase() {
    await this.newPurchaseButton.click();
    await this.page.waitForURL('**/purchases/cart');
  }

  /**
   * Click the Browse Products button (shown in empty state)
   */
  async clickBrowseProducts() {
    await this.browseProductsButton.click();
    await this.page.waitForURL('**/products');
  }

  /**
   * Wait for purchase data to be updated (useful after making a purchase)
   */
  async waitForDataRefresh() {
    await this.page.waitForTimeout(2000); // Give time for data to update
    await this.page.reload();
    await this.waitForPageLoad();
  }

  /**
   * Get the most recent purchase
   */
  async getMostRecentPurchase(): Promise<{
    date: string;
    quantity: string;
    amount: string;
    status: string;
  } | null> {
    const rowCount = await this.getPurchaseRowsCount();
    if (rowCount === 0) {
      return null;
    }
    
    return await this.getPurchaseRowData(0); // Assuming first row is most recent
  }
}