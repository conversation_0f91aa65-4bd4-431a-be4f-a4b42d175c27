import { Page, Locator, expect } from '@playwright/test';

export class PatientDashboardPage {
  readonly page: Page;
  readonly pendingQuestionnairesSection: Locator;
  readonly completedQuestionnairesSection: Locator;
  readonly viewAllPendingButton: Locator;
  readonly viewAllCompletedButton: Locator;
  readonly healthSummaryCard: Locator;
  readonly exploreHealthDataButton: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Main sections
    this.pendingQuestionnairesSection = page.locator('text=Pending Questionnaires').locator('..');
    this.completedQuestionnairesSection = page.locator('text=Recently Reviewed').locator('..');
    
    // Navigation buttons
    this.viewAllPendingButton = page.locator('button:has-text("View All")').first();
    this.viewAllCompletedButton = page.locator('button:has-text("View All")').nth(1);
    
    // Health summary
    this.healthSummaryCard = page.locator('text=Health Summary').locator('..');
    this.exploreHealthDataButton = page.locator('button:has-text("Explore Health Data")');
  }

  async goto() {
    await this.page.goto('/trq/dashboard');
    await this.waitForDashboardToLoad();
  }

  async waitForDashboardToLoad() {
    // Wait for the main dashboard elements to be visible
    await expect(this.pendingQuestionnairesSection).toBeVisible({ timeout: 10000 });
    await expect(this.completedQuestionnairesSection).toBeVisible({ timeout: 10000 });
  }

  /**
   * Get a pending questionnaire item by title
   */
  getPendingQuestionnaireItem(title: string): Locator {
    return this.pendingQuestionnairesSection.locator(`text=${title}`).locator('..');
  }

  /**
   * Get the start/continue button for a specific questionnaire
   */
  getQuestionnaireActionButton(title: string): Locator {
    const questionnaireItem = this.getPendingQuestionnaireItem(title);
    return questionnaireItem.locator('button:has-text("Start"), button:has-text("Continue")');
  }

  /**
   * Click on a questionnaire to start or continue it
   */
  async clickQuestionnaire(title: string) {
    const actionButton = this.getQuestionnaireActionButton(title);
    await expect(actionButton).toBeVisible({ timeout: 10000 });
    await actionButton.click();
  }

  /**
   * Verify that a questionnaire appears in the pending list
   */
  async verifyPendingQuestionnaireExists(title: string) {
    const questionnaireItem = this.getPendingQuestionnaireItem(title);
    await expect(questionnaireItem).toBeVisible({ timeout: 10000 });
  }

  /**
   * Verify that a questionnaire has the expected status
   */
  async verifyQuestionnaireStatus(title: string, expectedStatus: 'Start' | 'Continue') {
    const actionButton = this.getQuestionnaireActionButton(title);
    await expect(actionButton).toHaveText(expectedStatus);
  }

  /**
   * Navigate to all questionnaires page
   */
  async navigateToAllQuestionnaires() {
    await this.viewAllPendingButton.click();
    await expect(this.page).toHaveURL(/.*questionnaires/);
  }

  /**
   * Get completed questionnaire item by title
   */
  getCompletedQuestionnaireItem(title: string): Locator {
    return this.completedQuestionnairesSection.locator(`text=${title}`).locator('..');
  }

  /**
   * Verify that a questionnaire appears in the completed/reviewed list
   */
  async verifyCompletedQuestionnaireExists(title: string) {
    const questionnaireItem = this.getCompletedQuestionnaireItem(title);
    await expect(questionnaireItem).toBeVisible({ timeout: 10000 });
  }

  /**
   * Check if there are no pending questionnaires
   */
  async verifyNoPendingQuestionnaires() {
    const noPendingMessage = this.pendingQuestionnairesSection.locator('text=No pending questionnaires');
    await expect(noPendingMessage).toBeVisible();
  }

  /**
   * Check if there are no completed questionnaires
   */
  async verifyNoCompletedQuestionnaires() {
    const noCompletedMessage = this.completedQuestionnairesSection.locator('text=No reviewed questionnaires yet');
    await expect(noCompletedMessage).toBeVisible();
  }

  /**
   * Navigate to health summary page
   */
  async navigateToHealthSummary() {
    await this.exploreHealthDataButton.click();
    await expect(this.page).toHaveURL(/.*health-summary/);
  }

  /**
   * Wait for questionnaire data to load
   */
  async waitForQuestionnaireDataToLoad() {
    // Wait for either questionnaires to appear or "no questionnaires" message
    await Promise.race([
      this.page.waitForSelector('text=No pending questionnaires', { timeout: 10000 }),
      this.page.waitForSelector('button:has-text("Start"), button:has-text("Continue")', { timeout: 10000 })
    ]);
  }

  /**
   * Get the count of pending questionnaires displayed
   */
  async getPendingQuestionnaireCount(): Promise<number> {
    const startButtons = await this.pendingQuestionnairesSection.locator('button:has-text("Start")').count();
    const continueButtons = await this.pendingQuestionnairesSection.locator('button:has-text("Continue")').count();
    return startButtons + continueButtons;
  }

  /**
   * Get the count of completed questionnaires displayed
   */
  async getCompletedQuestionnaireCount(): Promise<number> {
    return await this.completedQuestionnairesSection.locator('button:has-text("View")').count();
  }
}
