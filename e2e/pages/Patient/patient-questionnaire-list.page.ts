import { Page, Locator, expect } from '@playwright/test';

export class PatientQuestionnaireListPage {
  readonly page: Page;
  readonly dataGrid: Locator;
  readonly searchInput: Locator;
  readonly statusFilter: Locator;
  readonly toolbar: Locator;
  readonly loadingIndicator: Locator;
  readonly noDataMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Main data grid for questionnaires
    this.dataGrid = page.locator('[data-testid="my-questionnaires-data-grid"] .MuiDataGrid-root');
    
    // Search and filter controls
    this.searchInput = page.locator('input[placeholder*="Search"]');
    this.statusFilter = page.locator('select[data-testid*="status-filter"]');
    
    // Toolbar and loading states
    this.toolbar = page.locator('.MuiDataGrid-toolbarContainer');
    this.loadingIndicator = page.locator('.MuiCircularProgress-root');
    this.noDataMessage = page.locator('text=No questionnaires found');
  }

  async goto() {
    await this.page.goto('/trq/questionnaires');
    await this.waitForPageToLoad();
  }

  async gotoMyQuestionnaires() {
    await this.page.goto('/trq/questionnaires/my-questionnaires');
    await this.waitForPageToLoad();
  }

  async waitForPageToLoad() {
    // Wait for either the data grid to load or no data message to appear
    await Promise.race([
      this.dataGrid.waitFor({ state: 'visible', timeout: 15000 }),
      this.noDataMessage.waitFor({ state: 'visible', timeout: 15000 })
    ]);
    
    // Wait for loading to complete
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get a questionnaire row by title
   */
  getQuestionnaireRow(title: string): Locator {
    return this.dataGrid.locator(`.MuiDataGrid-row:has-text("${title}")`);
  }

  /**
   * Get the action button for a specific questionnaire row
   */
  getQuestionnaireActionButton(title: string): Locator {
    const row = this.getQuestionnaireRow(title);
    return row.locator('button:has-text("Start"), button:has-text("Continue"), button:has-text("View")');
  }

  /**
   * Click on a questionnaire to open it
   */
  async clickQuestionnaire(title: string) {
    const actionButton = this.getQuestionnaireActionButton(title);
    await expect(actionButton).toBeVisible({ timeout: 10000 });
    await actionButton.click();
  }

  /**
   * Click on a questionnaire row to view details
   */
  async clickQuestionnaireRow(title: string) {
    const row = this.getQuestionnaireRow(title);
    await expect(row).toBeVisible({ timeout: 10000 });
    await row.click();
  }

  /**
   * Verify that a questionnaire exists in the list
   */
  async verifyQuestionnaireExists(title: string) {
    const row = this.getQuestionnaireRow(title);
    await expect(row).toBeVisible({ timeout: 10000 });
  }

  /**
   * Verify questionnaire status
   */
  async verifyQuestionnaireStatus(title: string, expectedStatus: string) {
    const row = this.getQuestionnaireRow(title);
    const statusCell = row.locator('.MuiDataGrid-cell[data-field="status"]');
    await expect(statusCell).toContainText(expectedStatus, { ignoreCase: true });
  }

  /**
   * Get questionnaire status from the row
   */
  async getQuestionnaireStatus(title: string): Promise<string> {
    const row = this.getQuestionnaireRow(title);
    const statusCell = row.locator('.MuiDataGrid-cell[data-field="status"]');
    return await statusCell.textContent() || '';
  }

  /**
   * Search for questionnaires
   */
  async searchQuestionnaires(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.waitForTimeout(1000); // Wait for search to process
  }

  /**
   * Filter questionnaires by status
   */
  async filterByStatus(status: string) {
    await this.statusFilter.selectOption(status);
    await this.page.waitForTimeout(1000); // Wait for filter to apply
  }

  /**
   * Get the count of questionnaires in the list
   */
  async getQuestionnaireCount(): Promise<number> {
    const rows = this.dataGrid.locator('.MuiDataGrid-row');
    return await rows.count();
  }

  /**
   * Verify that no questionnaires are displayed
   */
  async verifyNoQuestionnaires() {
    await expect(this.noDataMessage).toBeVisible({ timeout: 10000 });
  }

  /**
   * Wait for questionnaire data to load
   */
  async waitForQuestionnaireDataToLoad() {
    // Wait for loading indicator to disappear
    await this.loadingIndicator.waitFor({ state: 'hidden', timeout: 15000 });
    
    // Wait for either questionnaires to appear or no data message
    await Promise.race([
      this.dataGrid.locator('.MuiDataGrid-row').first().waitFor({ state: 'visible', timeout: 10000 }),
      this.noDataMessage.waitFor({ state: 'visible', timeout: 10000 })
    ]);
  }

  /**
   * Get all questionnaire titles from the current page
   */
  async getAllQuestionnaireTitles(): Promise<string[]> {
    const titleCells = this.dataGrid.locator('.MuiDataGrid-cell[data-field="title"]');
    const count = await titleCells.count();
    const titles: string[] = [];
    
    for (let i = 0; i < count; i++) {
      const title = await titleCells.nth(i).textContent();
      if (title) {
        titles.push(title.trim());
      }
    }
    
    return titles;
  }

  /**
   * Verify questionnaire assignment date
   */
  async verifyQuestionnaireAssignedDate(title: string, expectedDate?: string) {
    const row = this.getQuestionnaireRow(title);
    const assignedDateCell = row.locator('.MuiDataGrid-cell[data-field="assignedAt"], .MuiDataGrid-cell[data-field="createdAt"]');
    
    if (expectedDate) {
      await expect(assignedDateCell).toContainText(expectedDate);
    } else {
      // Just verify the date cell exists and has content
      await expect(assignedDateCell).not.toBeEmpty();
    }
  }

  /**
   * Navigate to questionnaire wizard/completion page
   */
  async navigateToQuestionnaireWizard(questionnaireId: string) {
    await this.page.goto(`/trq/questionnaires/wizard/${questionnaireId}`);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Verify questionnaire completion status
   */
  async verifyQuestionnaireCompleted(title: string) {
    await this.verifyQuestionnaireStatus(title, 'completed');
  }

  /**
   * Verify questionnaire is pending/assigned
   */
  async verifyQuestionnairePending(title: string) {
    const row = this.getQuestionnaireRow(title);
    const statusCell = row.locator('.MuiDataGrid-cell[data-field="status"]');
    const statusText = await statusCell.textContent();
    
    // Check for various pending status indicators
    const isPending = statusText?.toLowerCase().includes('pending') || 
                     statusText?.toLowerCase().includes('assigned') || 
                     statusText?.toLowerCase().includes('created');
    
    expect(isPending).toBeTruthy();
  }
}
