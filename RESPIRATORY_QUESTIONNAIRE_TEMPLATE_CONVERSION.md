# Respiratory Questionnaire Template Conversion

## Summary

Successfully converted the respiratory questionnaire from a static questionnaire into a template-based system that follows the existing questionnaire architecture while preserving the unique wizard-based user experience.

## Changes Made

### 1. Template Data Structure
- **File**: `src/Questionnaires/RespiratoryQuestionnaire/data/respiratoryQuestionnaireTemplate.ts`
- **Changes**: 
  - Added `respiratoryQuestionnaireTemplateData` - proper QuestionnaireTemplate structure
  - Maintained backward compatibility with existing `respiratoryQuestionnaireTemplate`
  - Added `wizardType: 'respiratory'` metadata for wizard detection
  - Included `respiratoryParts` structure for the wizard interface

### 2. Template Seeding Script
- **File**: `scripts/seedRespiratoryTemplate.ts`
- **Purpose**: Seeds the respiratory questionnaire template into the database
- **Usage**: `npx tsx scripts/seedRespiratoryTemplate.ts`
- **Result**: Creates template with ID `respiratory-health-questionnaire-template`

### 3. Instance Wizard Component
- **File**: `src/Questionnaires/RespiratoryQuestionnaire/components/RespiratoryInstanceWizard.tsx`
- **Purpose**: Handles respiratory questionnaire instances created from templates
- **Features**:
  - Preserves existing wizard interface and functionality
  - Works with template-to-instance pattern
  - Automatic questionnaire loading and validation
  - Integrated with standard questionnaire submission system

### 4. Instance Service Functions
- **File**: `src/Questionnaires/RespiratoryQuestionnaire/services/respiratoryInstanceService.ts`
- **Functions**:
  - `createRespiratoryQuestionnaireInstance()` - Creates instances from template
  - `submitRespiratoryQuestionnaireInstance()` - Submits completed instances
  - `getRespiratoryQuestionnaireInstancesForUser()` - Retrieves user's instances
  - `batchCreateRespiratoryInstances()` - Bulk instance creation

### 5. Wizard Type Detection
- **File**: `src/Questionnaires/Wizard/QuestionnaireWizard.tsx`
- **Changes**: 
  - Added automatic detection of respiratory questionnaires
  - Routes respiratory instances to `RespiratoryInstanceWizard`
  - Maintains backward compatibility with standard questionnaires

### 6. Updated Exports
- **File**: `src/Questionnaires/RespiratoryQuestionnaire/index.ts`
- **Changes**: 
  - Exported `RespiratoryInstanceWizard` component
  - Exported `respiratoryInstanceService` functions

### 7. Documentation Updates
- **File**: `src/Questionnaires/RespiratoryQuestionnaire/README.md`
- **Changes**: 
  - Added template-based architecture documentation
  - Documented workflow and components
  - Added seeding script instructions

## Architecture

### Template-to-Instance Flow

1. **Template Creation**: 
   - Respiratory template seeded into `questionnaireTemplates` collection
   - Contains all questions and metadata including `wizardType: 'respiratory'`

2. **Instance Creation**:
   - Administrators use Templates page to create instances
   - Standard `createQuestionnaire()` function creates instance from template
   - Instance inherits template metadata including wizard type

3. **Assignment**:
   - Standard `assignQuestionnaireToPatient()` assigns instance to user
   - Instance appears in user's questionnaire list

4. **Completion**:
   - User accesses instance via standard wizard route `/trq/questionnaires/wizard/:id`
   - `QuestionnaireWizard` detects respiratory type and routes to `RespiratoryInstanceWizard`
   - User completes questionnaire with preserved wizard interface

5. **Submission**:
   - Standard `submitQuestionnaireResponse()` handles submission
   - Response stored in `questionnaireResponses` collection
   - Questionnaire status updated to completed

### Wizard Type Detection

The system detects respiratory questionnaires using multiple criteria:
- `metadata.wizardType === 'respiratory'`
- `templateId === 'respiratory-health-questionnaire-template'`
- `category === 'Respiratory Health'`

### Backward Compatibility

- Original `RespiratoryQuestionnaireWizard` maintained for static route `/trq/questionnaires/respiratory`
- Existing respiratory questionnaire data structure preserved
- No breaking changes to existing functionality

## Benefits

1. **Consistency**: Respiratory questionnaires now follow the same template-to-instance pattern as other questionnaires
2. **Flexibility**: Administrators can create multiple instances and assign them to different users
3. **Management**: Instances can be tracked, reviewed, and managed like other questionnaires
4. **Scalability**: Supports bulk creation and assignment of respiratory questionnaires
5. **Integration**: Fully integrated with existing questionnaire management system

## Issues Resolved

### Issue 1: Missing Template in Templates Page
**Problem**: Seeded template not appearing in `/trq/templates`
**Root Cause**: `convertToTemplate` function missing required fields (`name`, `isPublished`, `version`)
**Solution**: Updated `convertToTemplate` in `questionnaireTemplateService.ts` to include all required fields
**Status**: ✅ **RESOLVED** - Template now appears in templates list

### Issue 2: Legacy Respiratory Questionnaire Button
**Problem**: Static respiratory questionnaire button still present on `/trq/questionnaires`
**Root Cause**: Button in `QuestionnaireToolbar` pointing to old static route
**Solution**:
- Updated button text to "Respiratory Template"
- Changed navigation to redirect to respiratory template details page
- Users now create instances from template instead of using static route
**Status**: ✅ **RESOLVED** - Button now directs to template-based workflow

## Testing

The implementation has been tested with:
- ✅ Template seeding script execution
- ✅ Template data structure validation
- ✅ Wizard type detection logic
- ✅ Component compilation and exports
- ✅ Service function implementation
- ✅ Fixed import/export issues with questionnaire service
- ✅ Development server running without compilation errors
- ✅ Template visibility in database verified (20 total templates including respiratory)
- ✅ All required template fields present and properly formatted
- ✅ Legacy button redirected to template-based workflow

## Next Steps

1. Test complete workflow in browser:
   - Navigate to Templates page
   - Find respiratory questionnaire template
   - Create and assign instance to user
   - Complete questionnaire as user
   - Verify submission and review process

2. Consider adding:
   - Bulk assignment interface for respiratory questionnaires
   - Specialized reporting for respiratory questionnaire compliance
   - Integration with existing respiratory questionnaire analytics

## Files Modified

- `src/Questionnaires/RespiratoryQuestionnaire/data/respiratoryQuestionnaireTemplate.ts`
- `src/Questionnaires/RespiratoryQuestionnaire/index.ts`
- `src/Questionnaires/RespiratoryQuestionnaire/README.md`
- `src/Questionnaires/Wizard/QuestionnaireWizard.tsx`

## Files Created

- `scripts/seedRespiratoryTemplate.ts`
- `src/Questionnaires/RespiratoryQuestionnaire/components/RespiratoryInstanceWizard.tsx`
- `src/Questionnaires/RespiratoryQuestionnaire/services/respiratoryInstanceService.ts`
- `RESPIRATORY_QUESTIONNAIRE_TEMPLATE_CONVERSION.md`
