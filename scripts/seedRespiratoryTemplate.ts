/**
 * <PERSON><PERSON><PERSON> to seed the Respiratory Health Questionnaire template into the database
 * This allows administrators to create instances of the respiratory questionnaire
 * and assign them to users following the template-to-instance pattern.
 */

import admin from 'firebase-admin';
import { Timestamp } from 'firebase-admin/firestore';
import { respiratoryQuestionnaireTemplateData } from '../src/Questionnaires/RespiratoryQuestionnaire/data/respiratoryQuestionnaireTemplate';

// Initialize Firebase Admin SDK
import serviceAccount from '../service-account.json' with { type: 'json' };

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as any),
  });
}

const db = admin.firestore();

/**
 * Seed the respiratory questionnaire template
 */
async function seedRespiratoryTemplate() {
  try {
    console.log('Starting to seed respiratory questionnaire template...');

    // Check if template already exists
    const templateRef = db.collection('questionnaireTemplates').doc(respiratoryQuestionnaireTemplateData.id);
    const existingTemplate = await templateRef.get();

    if (existingTemplate.exists) {
      console.log('Respiratory questionnaire template already exists. Updating...');
    } else {
      console.log('Creating new respiratory questionnaire template...');
    }

    // Prepare template data with timestamps
    const templateData = {
      ...respiratoryQuestionnaireTemplateData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      // Ensure metadata includes the wizard type
      metadata: {
        ...respiratoryQuestionnaireTemplateData.metadata,
        wizardType: 'respiratory'
      }
    };

    // Save the template
    await templateRef.set(templateData, { merge: true });

    console.log('✅ Respiratory questionnaire template seeded successfully!');
    console.log(`Template ID: ${respiratoryQuestionnaireTemplateData.id}`);
    console.log(`Template Name: ${respiratoryQuestionnaireTemplateData.name}`);
    console.log(`Questions Count: ${respiratoryQuestionnaireTemplateData.questions.length}`);
    console.log(`Category: ${respiratoryQuestionnaireTemplateData.category}`);
    console.log(`Wizard Type: ${templateData.metadata.wizardType}`);

  } catch (error) {
    console.error('❌ Error seeding respiratory questionnaire template:', error);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    await seedRespiratoryTemplate();
    console.log('\n🎉 Seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('\n💥 Seeding failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { seedRespiratoryTemplate };
