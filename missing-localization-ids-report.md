# Missing Localization IDs Report

## Summary
- **Total unique missing IDs**: 192
- **Total occurrences**: 244
- **Files affected**: 35

## Missing IDs by Category

### 1. Missing in BOTH en.json and es.json (70 IDs)

These IDs are completely missing from both localization files:

- `action-list.view`
- `action-list.edit`
- `action-list.delete`
- `filter`
- `value`
- `permissions.all_resources`
- `permissions.management`
- `permissions.description`
- `permissions.role.title`
- `permissions.admin.info`
- `permissions.resource`
- `permissions.save`
- `permissions.save.success`
- `role.management`
- `user.management`
- `users.by.role`
- `view.as`
- `last.login`
- `never`
- `no.users.with.role`
- `no-permission-patients`
- `my-patients`
- `this-patient`
- `required`
- `no-permission`
- `joined-date`
- `no-clinics-assigned`
- `permissions`
- `edit-admin`
- `enums.clearance.not_cleared`
- `enums.clearance.partially_cleared`
- `enums.clearance.mostly_cleared`
- `enums.clearance.fully_cleared`
- `enums.severity.low`
- `enums.severity.medium`
- `enums.severity.high`
- `respirator-clearance-description-required`
- `button.go-home`
- `button.go-back`
- `gender-required`
- `invalid-date-format`
- `email-already-in-use`
- `loading-clients`
- `error.fetch.doctors`
- `patient-not-found`
- `no-doctor-assigned-info`
- `patient-id-missing`
- `patient-info`
- `full-name`
- `medical-history`
- `new-purchase`
- `total-purchases`
- `pending-purchases`
- `questionnaire-breakdown`
- `filter-purchases`
- `no-matching-purchases`
- `no-purchases-yet`
- `search.patients`
- `no.patients.match.search`
- `no.patients.found`
- `unassigned`
- `not.applicable`
- `doctor.clinic`
- `enums.questionnaire_status.created`
- `enums.questionnaire_status.in_progress`
- `enums.questionnaire_status.completed`
- `enums.questionnaire_status.reviewed`
- `enums.questionnaire_status.assigned`
- `saving`
- `questionnaire-created-successfully`
- `edit-questionnaire`
- `questionnaire-updated-successfully`
- `my-questionnaires`
- `started-at`
- `start`
- `questionnaire-id-not-provided`
- `resume`
- `questionnaire-info`
- `template-info`
- `template-name`
- `template-title`
- `template-version`
- `template-category`
- `template-published`
- `published`
- `no-template-info`
- `not-provided`
- `no-patient-assigned`
- `no-client-assigned`
- `review-questionnaire`
- `save-review`
- `roles.admin_description`
- `roles.clinic_admin_description`
- `roles.client_description`
- `roles.doctor_description`
- `roles.patient_description`
- `resources.users`
- `resources.questionnaires`
- `resources.patients`
- `resources.reports`
- `resources.clinic_settings`
- `resources.system_settings`
- `resources.own_profile`
- `resources.own_questionnaires`
- `resources.own_reports`
- `actions.create`
- `actions.read`
- `actions.update`
- `actions.delete`
- `actions.list`
- `actions.manage`
- `edit-user`
- `role-clinic-admin`
- `role-required`
- `personal-info`
- `userName`
- `onboarded`
- `invite-status`
- `migration-info`
- `user-is-migrated`
- `user-not-migrated`
- `patient-details`
- `specialty`
- `metadata`
- `reminder-emails-sent`
- `no-users-selected`
- `migration-status`
- `user-migration`
- `send-reminder-emails`

### 2. Missing ONLY in es.json (121 IDs)

These IDs exist in en.json but are missing from es.json:

- `clear-search`
- `column`
- `apply`
- `client-not-found`
- `back-to-clients`
- `created`
- `last-updated`
- `user-id`
- `gender`
- `search-patients`
- `delete-multiple-patients-confirmation`
- `clinic`
- `clinic-admin-dashboard`
- `all-clinic-admins`
- `clinic-admin`
- `delete-multiple-admins-confirmation`
- `this-admin`
- `admin-id-not-provided`
- `user-not-admin`
- `no-permission-view-admin`
- `error-updating-admin`
- `activity`
- `administrator-permissions`
- `low`
- `medium`
- `high`
- `date-of-birth-required`
- `client-required`
- `male`
- `female`
- `other`
- `doctor-selection-required`
- `add-administrator`
- `administrators-added-successfully`
- `error-adding-administrators`
- `adding`
- `created-at`
- `continue`
- `error-fetching-questionnaire`
- `auth-uid`
- `completed-purchases`
- `browse-products`
- `error.fetch.assignment.data`
- `assigning.single`
- `assigning.multiple`
- `assign.questionnaire.title`
- `client.company`
- `assigned.doctor`
- `email-required`
- `active-status`
- `account-info`
- `updated-at`
- `dob`
- `doctor-details`
- `client-details`
- `error-fetching-users`
- `error-sending-reminders`
- `assigned-clinics`
- `started`
- `profile` (only missing in es.json based on line 351)

### 3. Missing ONLY in en.json (1 ID)

This ID exists in es.json but is missing from en.json:

- `profile` (only missing in en.json based on the context)

## Recommendations

1. **Create missing translations**: Add all 192 missing IDs to the appropriate localization files.
2. **Consistency check**: Some IDs follow different naming conventions (dots vs hyphens). Consider standardizing.
3. **Enum translations**: Many enum-related translations are missing (`enums.clearance.*`, `enums.severity.*`, etc.)
4. **Resource and action translations**: Complete sets of resource and action translations are missing.
5. **Role descriptions**: All role description translations are missing.

## Files with Most Missing IDs

1. `src/Patient/[pages]/PatientDetails.tsx` - 12 missing IDs
2. `src/Questionnaires/[pages]/QuestionnaireDetails.tsx` - 18 missing IDs
3. `src/Users/<USER>/UserDetails.tsx` - 18 missing IDs
4. `src/RBAC/[types]/Permission.ts` - 18 missing IDs
5. `src/ComplianceReports/[pages]/EditComplianceReport.tsx` - 8 missing IDs